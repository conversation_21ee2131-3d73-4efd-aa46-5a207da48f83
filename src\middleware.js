import { NextResponse } from 'next/server';

export function middleware(request) {
  const token = request.cookies.get('token')?.value;
  const isLoginPage = request.nextUrl.pathname === '/login';

  // If trying to access login page with token, redirect to home
  if (isLoginPage && token) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // Protected routes
  const protectedPaths = ['/cart', '/checkout', '/cart-details', '/profile'];
  const isProtectedPath = protectedPaths.some(path => 
    request.nextUrl.pathname.startsWith(path)
  );

  // If trying to access protected route without token, redirect to login
  if (isProtectedPath && !token) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('returnTo', request.nextUrl.pathname);
    return NextResponse.redirect(loginUrl);
  }

  return NextResponse.next();
}

// Update matcher to include all protected routes
export const config = {
  matcher: [
    '/cart/:path*',
    '/checkout/:path*',
    '/login',
    '/cart-details',
    '/profile/:path*',
  ]
};
