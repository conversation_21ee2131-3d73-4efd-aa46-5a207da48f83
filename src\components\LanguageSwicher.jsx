import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { Icon } from '@iconify/react';

const LanguageSwitcher = () => {
  const { i18n } = useTranslation();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [savedLanguage, setSavedLanguage] = useState(null);
  const dropdownRef = useRef(null);
  const [isMounted, setIsMounted] = useState(false);

  // Detect if component is mounted
  useEffect(() => {
    setIsMounted(true); // Ensure this only runs in the client
  }, []);

  // Set the language after mount
  useEffect(() => {
    if (isMounted) {
      const storedLanguage = localStorage.getItem('language') || 'En';
      setSavedLanguage(storedLanguage);
      i18n.changeLanguage(storedLanguage);
    }
  }, [i18n, isMounted]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
    localStorage.setItem('language', lng);
    setSavedLanguage(lng);
    router.refresh();
    setIsOpen(false);
  };

  if (!isMounted) return null;

  return (
    <div className="relative text-sm">
      <button
        className="border p-2 max-sm:p-1 rounded flex items-center gap-2 text-gray-500"
        type="button"
        onClick={() => setIsOpen(!isOpen)} 
      >
        <Icon icon="tabler:world" width="16" height="16" className="text-gray-500" />
        <span>{savedLanguage}</span>
        <Icon icon="akar-icons:chevron-down" width="12" height="12" />
      </button>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute left-0 mt-2 w-40 max-sm:w-20 bg-white shadow-lg rounded-md overflow-hidden z-10"
        >
          <button
            onClick={() => changeLanguage('En')}
            className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-indigo-600 hover:text-white"
          >
            <Icon icon="twemoji:us" width="16" height="16" className="mr-2" />
            English
          </button>
          <button
            onClick={() => changeLanguage('BN')}
            className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-indigo-600 hover:text-white"
          >
            <Icon icon="twemoji:flag-bd" width="16" height="16" className="mr-2" />
            বাংলা
          </button>
        </div>
      )}
    </div>
  );
};

export default LanguageSwitcher;
