import { Icon } from "@iconify/react";
import { useState } from "react";
import useDataFetch from "@/hooks/useDataFetch";
import Image from "next/image";
import React from "react";
import Link from "next/link";
import Pagination from "@/components/Pagination";
import Loading from "@/app/loading";
import { useTranslation } from "react-i18next";
import useDelete from "@/hooks/useDelete";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { addToCart } from "@/store/features/cartSlice";
import Skeleton from "react-loading-skeleton";

const WishList = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const router = useRouter();
  const { mainProfileActiveTab } = useSelector((state) => state.profile);
  const [currentPage, setCurrentPage] = useState(1);
  const { data, isLoading, error } = useDataFetch({
    queryKey: ["get-order-list", currentPage],
    endPoint: `/wishlists?page=${currentPage}`,
  });
  const { mutate: deleteWishlist } = useDelete({
    queryKey: ["get-order-list", mainProfileActiveTab],
    endpoint: "wishlists",
  });

  const wishlistItems = data?.data || [];

  const handleBuyNow = (book) => {
    dispatch(addToCart(book));
    router.push("/cart-details");
  };

  // if (isLoading) {
  //   return <Loading />;
  // }

  return (
    <div>
      <div className="flex items-center justify-between w-full pb-5 border-b border-gray-300">
        <h2 className="text-xl font-semibold text-indigo-600">
          {t("profile.my.wishlist")}
        </h2>
      </div>
      {isLoading ? (
        <div className=" flex items-start justify-center">
          <div className="w-full max-w-7xl mx-auto p-5 2xl:px-0 flex gap-6">
            {/* Left Side (Book Image) */}
            <div className="w-32 h-36">
              <Skeleton height="100%" width="100%" borderRadius={8} />
            </div>

            {/* Right Side (Book Details) */}
            <div className="flex-1">
              {/* Title */}
              <Skeleton height={40} width="60%" className="mb-2" />

              {/* Author, Categories & Published Date */}
              <Skeleton height={25} width="50%" className="mb-1" />
              <Skeleton height={25} width="40%" className="mb-1" />
              <Skeleton height={25} width="30%" className="mb-4" />
            </div>
          </div>
        </div>
      ) : (
        <div className="py-3 space-y-5">
          {wishlistItems?.length > 0 ? (
            wishlistItems?.map((item) => (
              <div
                key={item.id}
                className="w-full flex items-center flex-col md:flex-row gap-3 border-gray-300"
              >
                <Image
                  src={
                    item?.ebook?.cover_image_url ||
                    "/assets/all-images/book-2.png"
                  }
                  alt={item?.ebook?.title || "reviewed book image"}
                  width={50}
                  height={50}
                  className="min-w-16 lg:min-w-20 max-h-28 rounded-lg"
                />
                <div className="text-sm space-y-1 text-gray-500 w-full">
                  <div className="flex max-sm:items-start items-center max-sm:flex-col justify-between w-full">
                    <h2 className="text-lg text-gray-800">
                      {item?.ebook?.title}
                    </h2>
                    <span className="bg-green-100 px-5 py-1.5 rounded-full text-green-500 ">
                      <Icon
                        icon="flat-color-icons:ok"
                        width="18"
                        height="18"
                        className="inline mr-2"
                      />
                      Available
                    </span>
                  </div>
                  <p>
                    {item?.ebook?.authors &&
                      item.ebook.authors
                        .map((author) => author?.name)
                        .join(", ")}
                  </p>
                  <p>
                    {item?.ebook?.publisher
                      ? item.ebook.publisher
                      : "Unknown Publisher"}
                  </p>
                  <div className="flex gap-3 md:justify-end">
                    <button
                      onClick={() => handleBuyNow(item?.ebook)}
                      className="px-4 py-1.5 rounded bg-indigo-600 text-white"
                    >
                      Buy Now
                    </button>
                    <button
                      onClick={() => deleteWishlist(item.id)}
                      className="px-4 py-1.5 rounded border border-indigo-600 text-indigo-600"
                    >
                      Remove from wishlist
                    </button>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <p className="text-center">{t("no.wishlist.found")}</p>
          )}
        </div>
      )}
      {data?.data?.total_pages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={data.data.total_pages}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
};

export default WishList;
