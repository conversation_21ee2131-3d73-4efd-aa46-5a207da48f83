'use client'


import { Icon } from "@iconify/react/dist/iconify.js";
import React, { useState } from "react";
import useDataFetch from "@/hooks/useDataFetch";
import { useParams, useRouter } from "next/navigation";
import Loading from "@/components/Loading";
import { BookCard } from "@/components/books/BookCard";

const RecentCategory = () => {
  const { id } = useParams();
  const router = useRouter();
  console.log(router.state)
  const [bookCategory, setBookCategory] = useState("Newest");
  const [showFilter, setShowFilter] = useState(false);
  const [selectedRating, setSelectedRating] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [isEbook, setIsEbook] = useState(true);

  const {
    data,
    isLoading: isLoading,
    refetch: isFetching,
  } = useDataFetch({
    queryKey: ["allbooks"],
    endPoint: `/genres/${id}`,
  });

  
  const categoryBooks = isEbook ? (data?.ebooks?.data || []) : (data?.books?.data || []);

  const categories = [
    "Newest",
    "Best Selling",
    "Price: Low - High",
    "Price: High - Low",
    "Most Reviewed",
  ];

  const demoCategories = [
    "ক্যাটাগরি ১",
    "ক্যাটাগরি ২",
    "ক্যাটাগরি ৩",
    "ক্যাটাগরি ৪",
  ];

  const handleRatingChange = (rating) => {
    setSelectedRating(rating);
  };

  const handleCategoryChange = (category) => {
    setSelectedCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category]
    );
  };

  // Filter categories based on search input
  const filteredCategories = demoCategories.filter((category) =>
    category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0 py-8">
      <div className="grid grid-cols-12 gap-5">
        {/* Sidebar */}
        <div
          className={`${
            showFilter
              ? "block fixed right-0 top-11 sm:top-16 z-20 bg-white h-full overflow-y-auto w-72 shadow-lg"
              : "hidden lg:block"
          }  lg:col-span-3 space-y-6 p-5`}
        >
          <div className="border rounded p-4 border-gray-300 relative">
            <Icon
              onClick={() => setShowFilter(false)}
              className="absolute -left-5 -top-4 p-1 lg:hidden"
              icon="akar-icons:cross"
              width="24"
              height="24"
            />
            <span className="border-b-2 pb-1 text-lg font-semibold text-gray-600 border-indigo-500 mb-4">
              বইয়ের ক্যাটাগরি
            </span>

            <div className="flex items-center group border rounded-md overflow-hidden border focus-within:border-indigo-500 bg-indigo-500 text-gray-700 w-full mt-4">
              <input
                type="text"
                placeholder="ক্যাটাগরি অনুসন্ধান করুন.."
                className="w-full px-3 py-1.5 outline-0 bg-gray-100"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <button className="px-4 text-white">
                <Icon
                  icon="mdi:magnify"
                  className="text-xl group-focus:text-white"
                />
              </button>
            </div>

            <div className="space-y-3 mt-3 max-h-48 overflow-y-auto">
              {filteredCategories?.length > 0 ? filteredCategories.map((category) => (
                <label
                  key={category}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    className="form-checkbox"
                    checked={selectedCategories.includes(category)}
                    onChange={() => handleCategoryChange(category)}
                  />
                  <span className="text-sm">{category}</span>
                </label>
              )) : <p className="text-xs text-gray-500">ক্যাটাগরি পাওয়া যায়নি</p>}
            </div>
          </div>

          <div>
            <label className="flex items-center space-x-2 border rounded p-4 border-gray-300 cursor-pointer">
              <input onChange={() => setIsEbook(!isEbook)} checked={isEbook} type="checkbox" className="form-checkbox" />
              <span className="text-lg font-semibold text-gray-600">ই-বুক</span>
            </label>
          </div>

          <div className="border rounded p-4 border-gray-300">
            <span className="border-b-2 pb-1 text-lg font-semibold text-gray-600 border-indigo-500 mb-4">
              বইয়ের লেখক
            </span>
            <div className="hidden md:flex items-center group border rounded-md overflow-hidden border focus-within:border-indigo-500 bg-indigo-500 text-gray-700 w-full mt-4">
              <input
                type="text"
                placeholder="লেখক অনুসন্ধান করুন.."
                className="w-full px-3 py-1.5 outline-0 bg-gray-100"
              />
              <button className="px-4 text-white">
                <Icon
                  icon="mdi:magnify"
                  className="text-xl group-focus:text-white"
                />
              </button>
            </div>

            <div className="space-y-3 mt-3 max-h-48 overflow-y-auto">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input type="checkbox" className="form-checkbox" />
                <span className="text-sm">লেখকের নাম ১</span>
              </label>
              <label className="flex items-center space-x-2 cursor-pointer">
                <input type="checkbox" className="form-checkbox" />
                <span className="text-sm">লেখকের নাম ২</span>
              </label>
            </div>
          </div>

          <div className="border rounded p-4 border-gray-300">
            <h4 className="text-sm font-semibold mb-2">বইয়ের রিভিউ</h4>
            <div className="space-y-2">
              {[5, 4, 3, 2, 1].map((rating) => (
                <label
                  key={rating}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <input
                    type="radio"
                    name="review"
                    className="form-radio"
                    checked={selectedRating === rating}
                    onChange={() => handleRatingChange(rating)}
                  />
                  <span className="text-sm">{"⭐".repeat(rating)}</span>
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div
          className={`
            ${showFilter ? "opacity-20 pointer-events-none" : ""}
            col-span-12 lg:col-span-9 space-y-4
          `}
          onClick={() => showFilter && setShowFilter(false)}
        >
          <h3 className="text-xl font-semibold">গণিত, বিজ্ঞান ও প্রযুক্তি</h3>

          <div className="hidden lg:flex items-center gap-2 md:gap-5 flex-wrap">
            {categories?.map((category, idx) => (
              <div
                key={idx}
                onClick={() => setBookCategory(category)}
                className={`${
                  bookCategory === category
                    ? "bg-indigo-500 text-white"
                    : "bg-indigo-100 text-indigo-500"
                } cursor-pointer px-3 py-1 rounded`}
              >
                {category}
              </div>
            ))}
          </div>

          <div className="lg:hidden flex items-center justify-between">
            <button
              onClick={() => setShowFilter(true)}
              className="flex items-center gap-2 bg-gray-100 rounded py-1 px-2"
            >
              <Icon icon="mage:filter" width="20" height="20" /> Filter
            </button>

            <div>
              <span className="font-semibold text-gray-600 mr-1">Sort By:</span>
              <select
                name="sort_by"
                id="sort_by"
                className="bg-gray-100 rounded py-1 px-2"
              >
                {categories?.map((category, idx) => (
                  <option key={idx} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
            {isLoading ? (
              <Loading />
            ) : categoryBooks?.length > 0 ? (
              categoryBooks.map((book) => (
                <BookCard key={book.id} book={book} />
              ))
            ) : (
              <p className="text-start text-gray-500">কোন বই পাওয়া যায়নি</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecentCategory;
