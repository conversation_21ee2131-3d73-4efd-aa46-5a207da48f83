"use client";

import { useEffect, useState, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import api from "@/lib/api";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { setMainProfileActiveTab } from "@/store/features/profileSlice";
import { useQueryClient } from "@tanstack/react-query";

const PaymentCallback = () => {
  const { t } = useTranslation();
  const [status, setStatus] = useState(null);
  const [errorMsg, setErrorMsg] = useState(null);
  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  
  // Add a ref to track if the request has been made
  const fetchedRef = useRef(false);
  
  const paymentID = searchParams.get("paymentID");
  const paymentStatus = searchParams.get("status");
  const signature = searchParams.get("signature");
  const apiVersion = searchParams.get("apiVersion");
  
  // Set initial status from URL
  useEffect(() => {
    setStatus(paymentStatus);
  }, [paymentStatus]);

  // Fetch payment data using fetch API instead of useQuery
  useEffect(() => {
    const fetchPaymentData = async () => {
      if (!paymentID || !paymentStatus) {
        setError(new Error("Missing required payment parameters"));
        setIsLoading(false);
        return;
      }

      try {
        const response = await api.get(`/payments/callback`, {
          params: {
            paymentID,
            status: paymentStatus,
            signature,
            apiVersion
          }
        });
        
        setData(response.data);
        
        // Handle success case
        if (paymentStatus === "success" && response.data?.transactionStatus === "Completed") {
          // should show toast here?
        }
      } catch (err) {
        setError(err);
      } finally {
        setIsLoading(false);
      }
    };

    // Only fetch if we haven't already
    if (paymentID && paymentStatus && !fetchedRef.current) {
      fetchedRef.current = true;
      fetchPaymentData();
    } else if (!paymentID || !paymentStatus) {
      setIsLoading(false);
    }
  }, [paymentID, paymentStatus, signature, apiVersion]);

  useEffect(() => {
    if (!isLoading && error) {
      setErrorMsg(error?.response)
    }
  }, [error, isLoading]);


  const transactionStatus = data?.transactionStatus;

  // Simplified rendering logic using helper functions
  const renderLoadingState = () => (
    <div className="flex justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
    </div>
  );

  const renderSuccessState = () => (
    <div className="text-green-600">
      <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
      </svg>
      <p className="text-xl mb-4">{t("payment.processed.successfully")}</p>
    </div>
  );

  const renderProcessingState = () => (
    <div className="text-indigo-600">
      <div className="flex justify-center mb-4">
        <div className="flex space-x-2">
          <div className="h-3 w-3 bg-indigo-600 rounded-full animate-bounce delay-100"></div>
          <div className="h-3 w-3 bg-indigo-600 rounded-full animate-bounce delay-300"></div>
          <div className="h-3 w-3 bg-indigo-600 rounded-full animate-bounce delay-500"></div>
        </div>
      </div>
      <p className="text-xl mb-4">{t("payment.waiting.confirmation")}</p>
    </div>
  );

  const renderFailedState = () => (
    <div className="text-red-600">
      <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
      </svg>
      <p className="text-xl mb-4">{t("payment.could.not.process")}</p>
      {error && <p className="text-sm mb-4">{ errorMsg?.data?.errors || t("payment.error.occurred")}</p>}
    </div>
  );

  const renderActionButtons = () => (
    <div className="flex items-center justify-center gap-2">
      <button
        onClick={() => router.push("/")}
        className="mt-4 border border-indigo-600 text-indigo-600 px-4 py-2 rounded-lg hover:bg-indigo-600 hover:text-white transition-all duration-300"
      >
        {t("go.to.home")}
      </button>
      {transactionStatus === "Completed" && (
        <button
          onClick={() => {
            queryClient.invalidateQueries(["my-book-list"]);
            dispatch(setMainProfileActiveTab(2));
            router.push("/profile");
          }}
          className="mt-4 border bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700"
        >
          {t("view.book")}
        </button>
      )}
    </div>
  );

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
        <h1 className="text-2xl font-bold mb-6">
          {isLoading ? t("payment.processing") : 
           error ? t("payment.failed") :
           status === "success" ? (transactionStatus === "Completed" ? t("payment.successful") || "Payment Successful" : t("payment.processing")) : 
           t("payment.failed")}
        </h1>
        
        {isLoading ? renderLoadingState() : (
          <div>
            {error ? renderFailedState() :
             status === "success" ? (
               transactionStatus === "Completed" ? renderSuccessState() : renderProcessingState()
             ) : renderFailedState()}

            {!isLoading && renderActionButtons()}
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentCallback;