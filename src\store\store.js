import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';

import authReducer from './features/authSlice';
import cartReducer from './features/cartSlice';
import profileReducer from './features/profileSlice';
import commonApiReducer from './features/commonApiSlice';
import commonReducer from './features/commonSice';

// Persist configuration
// Auth is now managed through cookies, so we don't need to persist it
const cartPersistConfig = {
  key: 'cart',
  storage,
  whitelist: ['cart']
};

const commonApiPersistConfig = {
  key: 'commonApi',
  storage,
  // whitelist: ['data', 'status']
};

export const makeStore = () => {
  const store = configureStore({
    reducer: {
      auth: authReducer, // No longer using persist for auth (using cookies instead)
      cart: persistReducer(cartPersistConfig, cartReducer),
      profile: profileReducer,
      commonApi: persistReducer(commonApiPersistConfig, commonApiReducer),
      common: commonReducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE', 'persist/PURGE']
        }
      })
  })

  const persistor = persistStore(store)

  return { store, persistor }
}
