import { Icon } from "@iconify/react";
import React from "react";
import Orders from "./history-components/Orders";
import BookList from "./history-components/BookList";
import WishList from "./history-components/WishList";
import Reviews from "./history-components/Reviews";
import BookLibrary from "./history-components/BookLibrary";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { setHistoryActiveCategory } from "@/store/features/profileSlice";

const MyHistory = () => {
  const {historyActiveCategory} = useSelector(state => state.profile);
  const dispatch = useDispatch();
  const {t} = useTranslation();

  const categories = [
    t('profile.book.list'),
    t('profile.orders'),
    t('profile.ebook.library'),
    t('profile.wishlist'),
    t('profile.reviews'),
  ];

  return (
    <div>
      <div className="w-full">
        <h2 className="text-xl font-semibold text-indigo-600">My History</h2>
        <div className="border border-gray-300 w-full my-3"></div>

        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
          <div className="col-span-1">
            <div className="col-span-1 bg-[#F8F8FF] p-4 rounded shadow">
              {categories.map((category, index) => (
                <div
                  key={index}
                  className={`py-2 w-full ${
                    historyActiveCategory === index
                      ? "text-white bg-indigo-700 rounded"
                      : "text-gray-600"
                  } focus:outline-none transition-all duration-100`}
                >
                  <button
                    onClick={() => dispatch(setHistoryActiveCategory(index))}
                    className={`px-3 w-full text-base flex items-center justify-between ${
                      index !== categories.length - 1 && ""
                    } border-gray-300 gap-2`}
                  >
                    {category}
                    <Icon icon="ep:right" className="text-lg" />{" "}
                  </button>
                </div>
              ))}
            </div>
          </div>

          <div className="col-span-1 md:col-span-2 lg:col-span-3">
            {historyActiveCategory === 0 && <BookList />}
            {historyActiveCategory === 1 && <Orders />}
            {historyActiveCategory === 2 && <BookLibrary />}
            {historyActiveCategory === 3 && <WishList />}
            {historyActiveCategory === 4 && <Reviews />}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MyHistory;
