import { Icon } from "@iconify/react";
import { useState } from "react";
import useDataFetch from "@/hooks/useDataFetch";
import Image from "next/image";
import React from "react";
import Link from "next/link";
import Pagination from "@/components/Pagination";
import Loading from "@/app/loading";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import Skeleton from "react-loading-skeleton";

const BookLibrary = () => {
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(1);
  const { token } = useSelector((state) => state.auth);
  const { data, isLoading, error } = useDataFetch({
    queryKey: ["my-book-list", currentPage],
    endPoint: `/my-ebooks?page=${currentPage}`,
  });

  const ebooks = data?.data || [];
  const totalPages = data?.data?.total_pages || 1;

  return (
    <div>
      <div className="flex items-center justify-between w-full pb-5 border-b border-gray-300">
        <h2 className="text-xl font-semibold text-indigo-600">
          {t("profile.ebook.library")}
        </h2>
      </div>
      {isLoading ? (
        <div className=" flex items-start justify-center">
          <div className="w-full max-w-7xl mx-auto p-5 2xl:px-0 flex gap-6">
            {/* Left Side (Book Image) */}
            <div className="w-32 h-36">
              <Skeleton height="100%" width="100%" borderRadius={8} />
            </div>

            {/* Right Side (Book Details) */}
            <div className="flex-1">
              {/* Title */}
              <Skeleton height={40} width="60%" className="mb-2" />

              {/* Author, Categories & Published Date */}
              <Skeleton height={25} width="50%" className="mb-1" />
              <Skeleton height={25} width="40%" className="mb-1" />
              <Skeleton height={25} width="30%" className="mb-4" />
            </div>
          </div>
        </div>
      ) : (
        <div className="py-3 space-y-3">
          {ebooks.length > 0 ? (
            ebooks?.map((ebook) => (
              <div
                key={ebook.id}
                className="border rounded-lg border-gray-300 flex items-center justify-between gap-5 p-4"
              >
                <div className="w-full flex items-center flex-col md:flex-row gap-3 border-gray-300">
                  <Image
                    src={
                      ebook.cover_image_url || "/assets/all-images/book-2.png"
                    }
                    alt={ebook.title}
                    width={50}
                    height={50}
                    className="min-w-16 lg:min-w-20 max-h-28 rounded-lg"
                  />

                  <div className="text-sm space-y-1 text-gray-500">
                    <p className="text-lg text-gray-800">{ebook.title}</p>
                    <p>
                      {ebook.authors &&
                        ebook.authors.map((author) => author.name).join(", ")}
                    </p>
                    <p>
                      {ebook.publisher ? ebook.publisher : "Unknown Publisher"}
                    </p>
                  </div>
                </div>
                <div className="border h-20 w-0"></div>
                <div className="w-full">
                  <h2>Reading Progress</h2>
                  <div>
                    <div className="text-right text-xs text-gray-400 mt-1">
                      {ebook.reading_progress}%
                    </div>
                    <div className="relative w-full h-2 bg-gray-200 rounded overflow-hidden">
                      <div
                        className="absolute top-0 left-0 h-full bg-green-400"
                        style={{ width: `${ebook.reading_progress}%` }}
                      ></div>
                    </div>
                  </div>
                  <Link
                    target="_blank"
                    href={`${process.env.NEXT_PUBLIC_BASE_CONFIG_Redirect_URL}/flipbooks/${ebook?.id}?token=${token}`}
                    className="inline-block border border-indigo-600 text-indigo-600 px-5 py-1 rounded mt-5"
                  >
                    {t("read.now")}
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <p className="text-center">{t("no.ebook.found")}</p>
          )}
        </div>
      )}
      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={data.data.total_pages}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
};

export default BookLibrary;
