'use client';
import { createSlice } from '@reduxjs/toolkit';
import api from '@/lib/api';
import { setToken, setUser, removeToken, removeUser, loadAuthFromCookies } from '@/lib/auth';

// Initialize state from cookies if available
const initialState = loadAuthFromCookies();

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    loginSuccess: (state, action) => {
      state.isLoading = false;
      state.isAuth = true;
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.error = null;
      
      // Save to cookies using utility functions
      setToken(action.payload.token);
      setUser(action.payload.user);
    },
    loginFailure: (state, action) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    logout: (state) => {
      // Reset auth state
      state.user = null;
      state.token = null;
      state.error = null;
      state.isAuth = false;
      
      // Clear auth cookies
      removeToken();
      removeUser();
    },
    // Add a new action to initialize auth state from cookies
    initFromCookies: (state) => {
      const authData = loadAuthFromCookies();
      state.isAuth = authData.isAuth;
      state.token = authData.token;
      state.user = authData.user;
      state.isLoading = authData.isLoading;
      state.error = authData.error;
    },
  },
});

export const { loginStart, loginSuccess, loginFailure, logout, initFromCookies } = authSlice.actions;
export default authSlice.reducer;

// Thunk for handling login
export const loginUser = (credentials) => async (dispatch) => {
  try {
    dispatch(loginStart());
    const response = await api.post('/login', credentials);
    
    dispatch(
      loginSuccess({
        user: response.user,
        token: response.token,
      })
    );
    return response;
  } catch (error) {
    dispatch(loginFailure(error?.response?.data?.message || 'Login failed'));
    throw error;
  }
};
