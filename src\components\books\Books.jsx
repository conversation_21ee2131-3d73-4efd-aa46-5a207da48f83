"use client";

import { Icon } from "@iconify/react/dist/iconify.js";
import React, { useEffect, useState } from "react";
import useDataFetch from "@/hooks/useDataFetch";
import { useParams, usePathname, useRouter } from "next/navigation";
import Loading from "@/components/Loading";
import { useTranslation } from "react-i18next";
import SearchItem from "@/components/books-filter/SearchItem";
import { BookCard } from "./BookCard";
import Pagination from "../Pagination";
import "react-loading-skeleton/dist/skeleton.css";
import { useDispatch, useSelector } from "react-redux";
import Image from "next/image";
import Skeleton from "react-loading-skeleton";

const Books = ({
  endPoint,
  showAuthorFilter,
  showCategoryFilter,
  data = [],
}) => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const router = useRouter();

  // Redux State (Cached Data)
  const { categories, authors, loading } = useSelector(
    (state) => state.commonApi
  );

  // Local State (Component-specific)
  const [bookCategory, setBookCategory] = useState("newest");
  const [showFilter, setShowFilter] = useState(false);
  const [selectedRating, setSelectedRating] = useState(null);
  const [selectedAuthors, setSelectedAuthors] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [isEbook, setIsEbook] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);

  // const {
  //   data,
  //   isLoading,
  //   refetch: isFetching,
  // } = useDataFetch({
  //   queryKey: [
  //     endPoint?.split("/")?.join("-"),
  //     endPoint,
  //     isEbook,
  //     selectedAuthors,
  //     selectedRating,
  //   ],
  //   endPoint: endPoint,
  //   params: {
  //     // book_type: isEbook ? "ebook" : "book",
  //     authors:
  //       selectedAuthors?.length > 0 ? selectedAuthors.join(",") : undefined,
  //     category:
  //       selectedCategories?.length > 0
  //         ? selectedCategories.join(",")
  //         : undefined,
  //     rating: selectedRating !== null ? selectedRating : undefined,
  //   },
  // });

  useEffect(() => {
    const params = new URLSearchParams();
    if (selectedAuthors?.length > 0) {
      params.set("authors", selectedAuthors.join(","));
    }
    if (selectedCategories?.length > 0) {
      params.set("category", selectedCategories.join(","));
    }
    if (selectedRating !== null) {
      params.set("rating", selectedRating);
    }
    router.push(`?${params.toString()}`, undefined, { shallow: true });
  }, [selectedAuthors, selectedCategories, selectedRating, router]);
  const isLoading = loading;

  // Derived Data
  const totalPages = data?.total_pages || 1;
  // let books = [];
  // let eBooks = [];
  // if(isEbook){
  //   eBooks = data?.data?.filter((book) => book?.format === "ebook");
  // }

  // Sorting Options
  const sortingItems = [
    { value: "newest", label: "Newest" },
    { value: "best_selling", label: "Best Selling" },
    { value: "price_low", label: "Low to High" },
    { value: "price_high", label: "High to Low" },
    { value: "most_reviewed", label: "Most Reviewed" },
  ];

  // Event Handlers
  const handleRatingChange = (rating) => setSelectedRating(rating);
  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0 py-8">
      <div className="grid grid-cols-12 gap-5">
        {/* Sidebar */}
        <div
          className={`${
            showFilter
              ? "block fixed right-0 top-11 sm:top-16 z-20 bg-white h-full overflow-y-auto w-72 shadow-lg"
              : "hidden lg:block"
          }  lg:col-span-3 space-y-0 p-5 lg:p-0`}
        >
          <div className="relative">
            <Icon
              onClick={() => setShowFilter(false)}
              className="absolute -left-5 -top-2 p-1 lg:hidden"
              icon="akar-icons:cross"
              width="24"
              height="24"
            />
          </div>

          {showAuthorFilter && (
            <div className="pb-5">
              <SearchItem
                items={authors}
                title={t("book.author")}
                searchPlaceHolder={t("search.author")}
                setSelectedItems={setSelectedAuthors}
                selectedItems={selectedAuthors}
                loading={loading}
              />
            </div>
          )}

          {showCategoryFilter && (
            <div className="pb-5">
              <SearchItem
                items={categories}
                title={t("book.category")}
                searchPlaceHolder={t("search.category")}
                setSelectedItems={setSelectedCategories}
                selectedItems={selectedCategories}
                loading={loading}
              />
            </div>
          )}

          {/* <div>
            <label className="flex items-center space-x-2 border rounded p-4 border-gray-300 cursor-pointer">
              <input
                onChange={() => setIsEbook(!isEbook)}
                checked={isEbook}
                type="checkbox"
                className="form-checkbox"
              />
              <span className="text-lg font-semibold text-gray-600">
                {t("e.book")}
              </span>
            </label>
          </div> */}

          <div className="border rounded p-4 border-gray-300">
            <span className="text-lg font-semibold text-gray-600 border-indigo-500 border-b-2 pb-1">
              {t("book.reviews")}
            </span>
            <div className="space-y-2 mt-4">
              {[5, 4, 3, 2, 1].map((rating) => (
                <label
                  key={rating}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <input
                    type="radio"
                    name="review"
                    className="form-radio"
                    checked={selectedRating === rating}
                    onChange={() => handleRatingChange(rating)}
                  />
                  <span className="text-sm">{"⭐".repeat(rating)}</span>
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div
          className={`
            ${showFilter ? "opacity-20 pointer-events-none" : ""}
            col-span-12 lg:col-span-9
          `}
          onClick={() => showFilter && setShowFilter(false)}
        >
          {endPoint?.includes("author") ? (
            <div className="flex items-center gap-3 pb-4">
              <Image
                src={
                  data?.optional?.photo
                    ? data?.optional?.photo
                    : "/assets/all-images/profileAvater.png"
                }
                width={40}
                height={40}
                alt={data?.optional?.name || "Author image"}
                className="w-12 h-12 rounded-full"
              />
              <h3 className="text-xl font-semibold">{data?.optional?.name}</h3>
            </div>
          ) : (
            <h3 className="text-xl font-semibold pb-2">
              {data?.optional?.name}
            </h3>
          )}

          <div className="hidden lg:flex items-center gap-2 md:gap-5 flex-wrap pb-4">
            {sortingItems?.map((category, idx) => (
              <div
                key={idx}
                onClick={() => setBookCategory(category.value)}
                className={`${
                  bookCategory === category?.value
                    ? "bg-indigo-500 text-white"
                    : "bg-indigo-100 text-indigo-500"
                } cursor-pointer px-3 py-1 rounded`}
              >
                {category?.label}
              </div>
            ))}
          </div>

          <div className="lg:hidden flex items-center justify-between pb-4">
            <button
              onClick={() => setShowFilter(true)}
              className="flex items-center gap-2 bg-gray-100 rounded py-1 px-2"
            >
              <Icon icon="mage:filter" width="20" height="20" /> Filter
            </button>

            <div>
              <span className="font-semibold text-gray-600 mr-1">Sort By:</span>
              <select
                name="sort_by"
                id="sort_by"
                className="bg-gray-100 rounded py-1 px-2"
                onChange={(e) => setBookCategory(e.target.value)}
              >
                {sortingItems?.map((category, idx) => (
                  <option key={idx} value={category?.value}>
                    {category?.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/*  */}
          {isLoading || loading ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-5 mt-5 pb-4">
              {[...Array(1)].map((_, idx) => (
                <div key={idx} className="p-3 border rounded-lg shadow-md w-52">
                  <Skeleton height={180} className="mb-2" /> {/* Book Image */}
                  <Skeleton height={20} width="80%" className="mb-1" />{" "}
                  {/* Title */}
                  <Skeleton height={15} width="70%" />
                  <Skeleton height={15} width="60%" />
                  <Skeleton height={30} />
                </div>
              ))}
            </div>
          ) : data?.data?.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5 min-h-80 pb-4">
              {data?.data?.map((book, idx) =>
                (isEbook && book?.format === "ebook") ||
                (!isEbook && (book?.format !== "ebook" || !book?.format)) ? (
                  <BookCard key={idx} book={book} />
                ) : (
                  <p key={idx} className="text-start text-gray-500">
                    {t("no.book.found")}
                  </p>
                )
              )}
            </div>
          ) : (
            <p className="text-start text-gray-500 pb-4">
              {t("no.book.found")}
            </p>
          )}
          {/*  */}

          {totalPages > 1 && (
            <div className="mt-4 flex justify-center pb-4">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Books;
