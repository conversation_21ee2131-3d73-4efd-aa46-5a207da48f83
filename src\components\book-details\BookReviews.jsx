"use client";

import { Icon } from "@iconify/react/dist/iconify.js";
import React, { useEffect, useState } from "react";
import { ErrorMessage, Field, Form, Formik } from "formik";
import { useTranslation } from "react-i18next";
import api from "@/lib/api";
import * as Yup from "yup";
import useDataFetch from "@/hooks/useDataFetch";
import { useQueryClient } from "@tanstack/react-query";
import Loading from "@/app/loading";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import Image from "next/image";
import Skeleton from "react-loading-skeleton";

const BookReviews = ({ bookDetails }) => {
  const { t } = useTranslation();
  const [screenWidth, setScreenWidth] = useState(0);
  const { isAuth } = useSelector((state) => state.auth);
  const router = useRouter();
  const [limit, setLimit] = useState(3);
  const [showAll, setShowAll] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();
  const [isOpen, setIsOpen] = useState(false);

  const {
    data,
    isLoading: isAllReviewsLoading,
    refetch: isAllReviewsFetching,
  } = useDataFetch({
    queryKey: ["allbooks", bookDetails?.id],
    endPoint: `/ebooks/${bookDetails?.id}/reviews`,
  });
  const allReviews = data?.data || [];
  const averageRating =
    allReviews?.length > 0
      ? (
          allReviews.reduce((sum, review) => sum + (review?.rating || 0), 0) /
          allReviews.length
        ).toFixed(1)
      : 0;

  useEffect(() => {
    if (typeof window !== "undefined") {
      setScreenWidth(window.innerWidth);

      const handleResize = () => setScreenWidth(window.innerWidth);
      window.addEventListener("resize", handleResize);

      return () => window.removeEventListener("resize", handleResize);
    }
  }, []);

  useEffect(() => {
    if (screenWidth < 640) {
      setLimit(1);
    } else if (screenWidth < 768) {
      setLimit(2);
    } else {
      setLimit(3);
    }
  }, [screenWidth]);

  const initialValues = {
    book_id: bookDetails?.id,
    rating: "",
    comment: "",
    saveDetails: false,
  };

  const onClose = () => {
    setIsOpen(false);
  };

  const handleReviewSubmit = async (values, { resetForm }) => {
    if (!isAuth) {
      router.push("/login?returnTo=/book-details/" + bookDetails?.slug);
      return;
    }

    try {
      setIsLoading(true);
      const response = await api.post(`/reviews`, values);
      if (response?.data?.book_id) {
        resetForm();
        onClose();
        queryClient.refetchQueries(`/ebooks/${bookDetails?.id}/reviews`);
        queryClient.refetchQueries(`/ebooks/${slug}`);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  const validationSchema = Yup.object({
    rating: Yup.string().required("Please provide a rating."),
  });

  const ratingsData = [5, 4, 3, 2, 1].map((stars) => ({
    stars,
    count: allReviews.filter((review) => review.rating === stars).length,
  }));

  const totalRatings = allReviews.length;

  return (
    <div>
      <div>
        {/*  : allReviews?.length < 1 ? (
              <p className="py-5 text-center">No Reviews Available</p>
            ) */}
        {
          <>
            {isAllReviewsLoading ? (
              <div className="py-5 mt-8">
                <Skeleton height={25} width="100%" className="mb-2" />
                <Skeleton height={25} width="100%" className="mb-2" />
                <Skeleton height={25} width="100%" className="mb-2" />
                <Skeleton height={25} width="100%" className="mb-2" />
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between border-b-2 mb-3 py-4 border-gray-400">
                  <h2 className="text-2xl max-sm:text-xl font-semibold">
                    পাঠকদের অভিমত জানুন
                  </h2>
                  {/* <a
                href="/all-reviews"
                className="flex items-center gap-2 hover:text-indigo-500 hover:gap-3 transition-all duration-300"
              >
                {t("see.all.review")}{" "}
                <Icon icon="ep:right" width="24" height="24" />
              </a> */}
                </div>

                <div className="space-y-[32px]">
                  <div className="flex flex-col md:flex-row gap-5 md:items-center justify-between w-full">
                    <div>
                      <h4 className="text-xl">{t("review.this.product")}</h4>
                      <div className="flex items-center gap-1 my-3">
                        <Icon
                          icon="ph:star-light"
                          width="32"
                          height="32"
                          className="text-[#F59E0B] mb-1"
                        />
                        <Icon
                          icon="ph:star-light"
                          width="32"
                          height="32"
                          className="text-[#F59E0B] mb-1"
                        />
                        <Icon
                          icon="ph:star-light"
                          width="32"
                          height="32"
                          className="text-[#F59E0B] mb-1"
                        />
                        <Icon
                          icon="ph:star-light"
                          width="32"
                          height="32"
                          className="text-[#F59E0B] mb-1"
                        />
                        <Icon
                          icon="ph:star-light"
                          width="32"
                          height="32"
                          className="text-[#F59E0B] mb-1"
                        />
                      </div>
                      <button
                        disabled={!bookDetails?.have_bought || bookDetails?.is_reviewed} // Disable when have_bought is false
                        onClick={() => setIsOpen(true)}
                        className={`border px-4 py-1.5 rounded-lg ${
                          (!bookDetails?.have_bought || bookDetails?.is_reviewed)
                            ? "bg-gray-200 text-gray-400 cursor-not-allowed" // Disabled state
                            : "text-indigo-600 border-indigo-600" // Enabled state
                        }`}
                      >
                        {bookDetails?.is_reviewed
                          ? "Review Added"
                          : "রিভিউ লিখুন"}
                      </button>
                    </div>

                    <div className="text-center">
                      <h1 className="text-4xl text-center">{averageRating}</h1>
                      <div className="flex items-center gap-1 justify-center">
                        {[...Array(5)].map((_, index) => {
                          const ratingValue = index + 1;

                          return (
                            <Icon
                              key={index}
                              icon={
                                averageRating >= ratingValue
                                  ? "material-symbols:star-rounded" // Full star
                                  : averageRating >= ratingValue - 0.5
                                  ? "material-symbols:star-half-rounded" // Half star
                                  : "material-symbols:star-outline-rounded" // Empty star
                              }
                              width="28"
                              height="28"
                              className="text-yellow-400"
                            />
                          );
                        })}
                      </div>
                      <p className="text-lg text-gray-500">
                        {allReviews?.length} {t("reviews")} &
                      </p>
                      <p className="text-lg text-gray-500">
                        {totalRatings} {t("rating")}
                      </p>
                    </div>

                    <div className="flex flex-col gap-2">
                      {ratingsData.map(({ stars, count }) => {
                        const percentage =
                          totalRatings > 0 ? (count / totalRatings) * 100 : 0;

                        return (
                          <div key={stars} className="flex items-center gap-2">
                            {/* Star Icons */}
                            <div className="flex items-center gap-1">
                              {[...Array(5)].map((_, index) => (
                                <Icon
                                  key={index}
                                  icon="material-symbols:star-rounded"
                                  width="28"
                                  height="28"
                                  className={
                                    index < stars
                                      ? "text-yellow-400"
                                      : "text-gray-300"
                                  }
                                />
                              ))}
                            </div>

                            {/* Progress Bar */}
                            <div className="w-40 bg-gray-200 rounded-full h-2 dark:bg-gray-200">
                              <div
                                className="bg-yellow-400 h-2 rounded-full"
                                style={{ width: `${percentage}%` }}
                              ></div>
                            </div>

                            {/* Rating Count */}
                            <span className="text-gray-600 text-sm">
                              {count}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  <div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-5 my-5">
                      {allReviews
                        ?.slice(0, showAll ? allReviews?.length : limit)
                        ?.map((review, idx) => (
                          <div
                            key={review?.id}
                            className={`p-4 pb-11 rounded-xl shadow-md border relative ${
                              // idx === 0
                              //   ? "bg-[#ECFDF5]"
                              //   : idx === 1
                              // ? "bg-[#FFF7ED]"
                              // :
                              "bg-[#F5F3FF]"
                            }`}
                          >
                            <div className="flex items-center gap-3 absolute -left-3 -top-3">
                              <Image
                                src={
                                  review?.user?.profile_picture_url
                                    ? review?.user?.profile_picture_url
                                    : "/assets/all-images/writer.jpg"
                                }
                                width={40}
                                height={40}
                                alt={review?.user?.name}
                                className="w-16 h-16 rounded-full p-2 bg-white"
                              />
                              <div>
                                <h3 className="font-semibold text-lg mt-2">
                                  {review?.user?.name}
                                </h3>
                              </div>
                            </div>
                            <div className="pt-7">
                              <p className="mt-3 text-gray-600">
                                {review?.comment}
                              </p>
                              <div className="mt-3 flex absolute bottom-3">
                                {Array.from({ length: 5 }).map((_, index) =>
                                  review.rating >= index + 1 ? (
                                    <Icon
                                      key={index}
                                      icon="material-symbols:star-rounded"
                                      width="24"
                                      height="24"
                                      className="text-yellow-400"
                                    />
                                  ) : (
                                    <Icon
                                      key={index}
                                      icon="material-symbols:star-rounded"
                                      width="24"
                                      height="24"
                                      className="text-gray-300"
                                    />
                                  )
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>

                    {allReviews?.length > limit && (
                      <div className="text-center">
                        <button
                          onClick={() => setShowAll(!showAll)}
                          className="px-8 py-1 rounded text-indigo-500 border-indigo-500 hover:bg-indigo-500 transition-all duration-200 hover:text-white border"
                        >
                          {showAll ? t("see.less") : t("see.all")}
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}
          </>
        }

        {/* Review Form Modal */}
        {isOpen && (
          <div className="fixed inset-0 flex items-center justify-center bg-gray-600 bg-opacity-50 backdrop-blur-sm z-50">
            <div className="bg-white rounded-lg shadow-lg md:min-w-96 md:min-w-[550px] max-w-[600px] relative animate-fadeIn">
              <div className="bg-gray-100 rounded-t-lg p-2 flex items-center justify-between border-b border-gray-300">
                <h3 className="text-lg">{t("add.review")}</h3>

                <Icon
                  onClick={onClose}
                  icon="icon-park-outline:close"
                  width="18"
                  height="18"
                  className="cursor-pointer"
                />
              </div>
              <div className="p-4">
                <div className="border-b border-gray-300 hover:border-gray-400 pb-3 mb-4 flex items-center flex-row gap-3 w-full">
                  <div className="relative">
                    <Image
                      className="w-20 rounded"
                      src={
                        bookDetails?.cover_image
                          ? process.env.NEXT_PUBLIC_BASE_URL +
                            "/storage/" +
                            bookDetails?.cover_image
                          : "/assets/all-images/book-2.png"
                      }
                      alt={bookDetails?.title}
                      height={400}
                      width={200}
                    />
                  </div>
                  <div className="space-y-1">
                    <h2 className="text-lg max-sm:text-sm text-gray-700">
                      {bookDetails?.title}
                    </h2>
                    <p className="text-sm max-sm:text-xs">
                      {bookDetails?.author ? bookDetails.author : "--"}
                    </p>
                    <p className="text-sm max-sm:text-xs">
                      {bookDetails?.publisher
                        ? bookDetails.publisher
                        : "Others"}
                    </p>
                    <div className="flex items-center gap-2 text-lg">
                      <span className="flex items-center font-semibold text-red-500">
                        <Icon icon="mdi:currency-bdt" width="20" height="20" />{" "}
                        {bookDetails?.final_price
                          ? parseInt(bookDetails?.final_price)
                          : 0}
                      </span>
                      <span className="flex items-center font-semibold line-through text-gray-400">
                        <Icon icon="mdi:currency-bdt" width="20" height="20" />{" "}
                        {bookDetails?.price ? parseInt(bookDetails?.price) : 0}
                      </span>
                      <span className="text-base text-indigo-600">
                        (
                        {bookDetails?.discount
                          ? parseInt(bookDetails?.discount)
                          : 0}
                        % ছাড়ে)
                      </span>
                    </div>
                  </div>
                </div>

                {/* <p className="text-gray-600 mb-4">{t("add.review.detail")}</p> */}
                <Formik
                  initialValues={initialValues}
                  validationSchema={validationSchema}
                  onSubmit={handleReviewSubmit}
                >
                  {({ errors }) => (
                    <Form className="space-y-5">
                      <div className="flex items-center justify-between flex-col sm:flex-row gap-5">
                        {/* Rating Field */}
                        <div className="flex-1 w-full">
                          <label
                            htmlFor="rating"
                            className="block font-medium text-center text-gray-700"
                          >
                            বইটির সম্পর্কে রেটিং দিন
                          </label>
                          <div
                            role="group"
                            aria-labelledby="rating"
                            className="flex gap-1 items-center justify-center mt-1"
                          >
                            {[1, 2, 3, 4, 5].map((star) => (
                              <Field key={star} name="rating">
                                {({ field, form }) => (
                                  <span
                                    onClick={() =>
                                      form.setFieldValue("rating", star)
                                    }
                                    className={`cursor-pointer ${
                                      form.values.rating >= star
                                        ? "text-yellow-400"
                                        : "text-gray-300"
                                    }`}
                                  >
                                    <Icon
                                      icon="material-symbols:star-rounded"
                                      width="32"
                                      height="32"
                                    />
                                  </span>
                                )}
                              </Field>
                            ))}
                          </div>
                          <ErrorMessage
                            name="rating"
                            component="div"
                            className="text-red-500 text-sm mt-1"
                          />
                        </div>
                      </div>

                      {/* Review Field */}
                      <div>
                        <label
                          htmlFor="comment"
                          className="block font-medium text-gray-700"
                        >
                          {t("write.short.review")}
                        </label>
                        <Field
                          as="textarea"
                          id="comment"
                          name="comment"
                          className="w-full border rounded-md px-3 py-2 min-h-24 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                        />
                        <ErrorMessage
                          name="comment"
                          component="div"
                          className="text-red-500 text-sm mt-1"
                        />
                      </div>

                      {/* Save Details Checkbox */}
                      {/* <div className="flex items-center">
                        <Field
                          type="checkbox"
                          id="saveDetails"
                          name="saveDetails"
                          className="mr-2"
                        />
                        <label
                          htmlFor="saveDetails"
                          className="text-sm text-gray-700"
                        >
                          {t("save.info")}
                        </label>
                      </div> */}

                      <div className="flex justify-end gap-2">
                        <button
                          onClick={onClose}
                          className="border border-indigo-600 text-indigo-600 px-4 py-1 rounded hover:bg-indigo-100"
                        >
                          Close
                        </button>
                        <button
                          type="submit"
                          className="bg-indigo-600 text-white px-4 py-1 rounded hover:bg-indigo-700 transition"
                          disabled={isLoading}
                        >
                          {isLoading ? "Submitting..." : t("submit")}
                        </button>
                      </div>
                    </Form>
                  )}
                </Formik>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BookReviews;
