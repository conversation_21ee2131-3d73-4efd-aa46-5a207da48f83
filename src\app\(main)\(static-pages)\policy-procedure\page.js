import Loading from "@/app/loading";
import HtmlData from "@/components/form/HtmlData";
import React from "react";

const SoftwareDevelopment = async () => {

  let isLoading = true;

  async function getData() {
    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/static-pages/policy-procedure`,
        {
          cache: "force-cache",
        }
      );
      if (!res.ok) {
        throw new Error("Failed to fetch data");
      }
      return res.json();
    } finally {
      isLoading = false;
    }
  }

  const data = await getData();

  if (isLoading) {
    return <Loading />;
  }
  return (
    <div className="max-w-7xl mx-auto p-5 2xl:px-0 my-3">
      {data?.data?.content && <HtmlData htmlData={data?.data?.content} />}
    </div>
  );
};

export default SoftwareDevelopment;
