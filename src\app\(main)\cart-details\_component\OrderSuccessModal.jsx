"use client";
import React from "react";
import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { setMainProfileActiveTab } from "@/store/features/profileSlice";
import { useQueryClient } from "@tanstack/react-query";

const OrderSuccessModal = ({ isOpen, onClose, isOnlinePayment }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg w-96 text-center relative animate-fadeIn">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700"
        >
          <Icon icon="mdi:close" width="20" height="20" />
        </button>

        {/* Success Icon */}
        <div className="flex justify-center mb-4">
          <Icon
            icon="mdi:check-circle"
            className="text-green-500"
            width="50"
            height="50"
          />
        </div>

        {/* Success Message */}
        <h2 className="text-lg font-semibold text-gray-800">
          {t("order.placed.successfully")}
        </h2>
        <p className="text-gray-600 mt-2">
          { isOnlinePayment ? t("order.make.bkash.payment") :t("order.has.been.confirmed") }
        </p>

        {/* Close Button */}
        {!isOnlinePayment && (
          <div className="flex items-center justify-center gap-2">
            <button
              onClick={() => router.push("/")}
              className="mt-4 border border-indigo-600 text-indigo-600 px-4 py-2 rounded-lg hover:bg-indigo-600 hover:text-white transition-all duration-300"
            >
              Go to Home
            </button>
            <button
              onClick={() => {
                queryClient.invalidateQueries(["my-book-list"]);
                dispatch(setMainProfileActiveTab(2));
                router.push("/profile");
              }}
              className="mt-4 border bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700"
            >
              {t("view.book")}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderSuccessModal;
