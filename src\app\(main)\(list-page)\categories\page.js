'use client';

import ItemList from "@/components/ItemList";
import useDataFetch from "@/hooks/useDataFetch";
import Loading from "@/app/loading";
import { useSearchParams } from "next/navigation";

const Categories = () => {
  const searchParams = useSearchParams();
  const search = searchParams.get('search');
  const page = searchParams.get('page');

  const { data, isLoading } = useDataFetch({
    queryKey: ["categories", { search, page }],
    endPoint: "/categories",
    params: { 
      search,
      page,
      per_page: 20
    }
  });

  // if (isLoading) return <Loading />;

  return <ItemList navigateTitle="All Categories" title="Categories" data={data} isLoading={isLoading} />;
};

export default Categories;
