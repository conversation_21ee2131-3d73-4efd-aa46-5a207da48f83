{"name": "ebook-ecommerce", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@iconify/react": "^5.2.0", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.5.1", "@tanstack/react-query": "^5.66.7", "axios": "^1.7.9", "formik": "^2.4.6", "i18next-resources-to-backend": "^1.2.1", "js-cookie": "^3.0.5", "lucide-react": "^0.475.0", "next": "15.1.7", "next-auth": "^4.24.11", "next-i18next": "^15.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-easy-crop": "^5.4.1", "react-i18next": "^15.4.1", "react-loading-skeleton": "^3.5.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "sonner": "^2.0.1", "sooner": "^1.1.4", "swiper": "^11.2.4", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1"}}