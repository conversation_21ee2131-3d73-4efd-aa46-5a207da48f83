{"nav.search.text": "Search by book name or author name...", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "try.ebook": "Try eBook Builder", "home": "Home", "category": "Category", "writer": "Writer", "publisher": "Publisher", "flush": "Flush", "collection": "Collection", "offer": "Offer", "others": "Others", "hello": "Hello", "welcome.to.yourepub": "Welcome to YourEpub!", "make.platform": "Make your own eBook selling platform.", "become.publisher": "Become a Publisher/Author", "all.offers": "All Offers", "recent.books": "Recent Books", "see.more": "See More", "book.categories": "Book Categories", "authors": "Authors", "off": "Off", "buy.now": "Buy Now", "added.to.cart": "Added to <PERSON><PERSON>", "prokashoni": "Publisher", "last.update": "Last Update", "other.books.of.author": "Other books of this author", "buy.ebook": "Buy eBook version", "read.now": "Read Now", "read.a.little": "Read a little", "view.book": "View Book", "go.to.home": "Go to Home", "share.this.book": "Share this book", "add.to.cart": "Add to cart", "readers.reviews": "Readers Reviews", "see.all.review": "See all reviews", "add.review": "Add Review", "add.review.detail": "Your email address will not be published. Required fields are marked.", "write.short.review": "Write a short review", "name": "Name", "email": "Email", "rating": "Rating", "save.info": "Save my name, email, and website in this browser for the next time I comment.", "submit": "Submit", "see.all": "See All", "see.less": "See Less", "review.this.product": "Review this product", "reviews": "Reviews", "see.book.details": "See Book Details", "summary": "Summary", "specification": "Specification", "add.to.wishlist": "Add to wishlist", "added.to.wishlist": "Added to wishlist", "book.category": "Book Category", "book.author": "Book Author", "e.book": "E-Book", "not.found": "Not Found", "search.category": "Search Category", "search.author": "Search Author", "book.reviews": "Book Reviews", "no.book.found": "No book available", "not.available": "Not available", "no.order.found": "No order has been placed", "no.wishlist.found": "Nothing has been added to the wishlist", "no.ebook.found": "No eBook has been added", "no.review.found": "No review has been found", "dropdown.profile": "Profile", "your.shopping.cart": "Your Shopping Cart", "cart.empty": "Your cart is empty", "base.price": "Base Price", "total.discount": "Total Discount", "total": "Total", "saved.total": "You saved total", "view.details": "View Details", "unknown": "Unknown", "select.all": "Select All", "related.books": "Related Books", "order.summary": "Order Summary", "enter.coupon": "Enter Coupon Code", "apply": "Apply", "subtotal": "Subtotal", "online.fee": "Online Fee", "discount": "Off", "payable.total": "Payable Total", "bkash.paymnet": "BKash Payment", "procced.to.checkout": "Procced to Checkout", "loading": "Loading", "order.placed.successfully": "Order Placed Successfully!", "order.has.been.confirmed": "Thank you for your purchase. Your order has been confirmed.", "order.make.bkash.payment": "Please make your bKash payment here.", "payment.processing": "Payment Processing", "payment.successful": "Payment Successful", "payment.failed": "Payment Failed", "payment.waiting.confirmation": "Waiting for payment confirmation...", "payment.processed.successfully": "Your payment has been processed successfully!", "payment.could.not.process": "Your payment could not be processed.", "payment.error.occurred": "An error occurred while processing your payment.", "payment.redirecting.profile": "Redirecting to your profile page...", "payment.verification.error": "Error verifying payment", "payment.successful.completed": "Payment successful and completed!", "all.authors": "All Authors", "see.all.books": "See all Books", "author.not.found": "Author not found", "add.name": "Add Name", "entar.updated.name": "Enter updated name", "e.book.purchased": "e <PERSON> Purchased", "book.purchased": "Book Purchased", "my.wishlist": "My Wish list", "details": "Details", "account.settings": "Account <PERSON><PERSON>", "billing.information": "Billing Information", "my.history": "My History", "edit": "Edit", "cancel": "Cancel", "save": "Save", "profile.bkash.account": "<PERSON><PERSON>ash Account", "profile.my.account": "My Account", "profile.edit": "Edit Profile", "profile.contact": "Contact", "profile.email": "Email", "profile.bio": "Bio", "profile.password": "Password", "confirm.password": "Confirm Password", "profile.write.name": "Write your name", "profile.write.contact": "Write your contact", "profile.write.email": "Write your email address", "profile.write.bio": "Write your bio", "profile.write.password": "Enter Password", "profile.in.your.cart": "In your cart", "profile.items.selected": "items are selected.", "profile.go.to.cart": "Go to cart", "profile.purchase.list": "Purchase List", "profile.ebook.name": "Ebook Name", "profile.author": "Author", "profile.purchase.date": "Purchase Date", "profile.invoice.no": "Invoice No", "profile.amount": "Amount", "profile.download.pdf": "Download PDF", "profile.your.order.id": "Your order ID", "profile.my.history": "My History", "profile.my.orders": "My Orders", "profile.my.wishlist": "My Wishlist", "profile.my.reviews": "My Reviews", "profile.book.list": "Book List", "profile.orders": "Orders", "profile.ebook.library": "Ebook Library", "elibrary.btn": "eLibrary", "profile.wishlist": "Wishlist", "profile.reviews": "Reviews", "profile.enter.current.pass": "Enter Current Password", "profile.enter.new.pass": "Enter New Password", "go.back": "Go Back", "auth.login.title": "Sign in", "or": "Or", "auth.no.account": "Don't have an account?", "auth.have.account": "Already have an account?", "auth.forgot.password": "Forgot password?", "auth.reset.password": "Reset password", "auth.required": "required", "auth.verify.email": "<PERSON><PERSON><PERSON>", "auth.verify": "Verify", "auth.sign.up": "Sign up", "auth.sign.up.title": "Enter the following information to sign up", "auth.sign.up.name": "Name", "auth.sign.up.name.placeholder": "Write your name", "auth.sign.up.email": "Email", "auth.sign.up.email.placeholder": "Write your email address", "auth.sign.up.password": "Password", "auth.sign.up.password.placeholder": "Give a password", "auth.sign.up.confirm.password": "Confirm Password", "auth.sign.up.confirm.password.placeholder": "Re-enter the password", "auth.sign.up.invalid.email": "Invalid email", "auth.sign.up.atleast.8": "At least 8 characters needed", "auth.sign.up.password.not.match": "Passwords must match", "our.phone": "Our Phone number", "footer.details": "YOURePUB is an interactive e-book platform that makes reading books easier, more enjoyable and more realistic. Various publications, authors and readers can all use this platform. After creating interactive e-books through various sophisticated features of the e-book builder, there is a user-friendly e-commerce site for buying and selling them. In a word, YOURePUB is a sophisticated e-book platform for creating and buying interactive e-books.", "footer.links": "Links", "footer.links.about.us": "About Us", "footer.links.our.clients": "Our Clients", "footer.links.software": "Software Development", "footer.links.policy": "Policy and Procedure", "footer.help": "Help", "footer.help.support": "Support", "footer.help.delivery": "Delivery Details", "footer.help.terms": "Terms & Conditions", "footer.help.policy": "Policy & Policy", "footer.location": "Location", "footer.location.address": "House # 13 (5th Floor), Block-C, Main Road, Banasree, Rampura, Dhaka-1219.", "footer.accept": "We Accept"}