import Image from "next/image";
import React from "react";
import { useTranslation } from "react-i18next";

const BillingInfo = () => {
  const {t} = useTranslation();
  return (
    <div>
      <div className="w-full">
        <h2 className="text-xl font-semibold border-b pb-5 text-indigo-600">
          {t('billing.information')}
        </h2>
        {/* <div className="border border-gray-300 w-full my-3"></div> */}
      </div>

      <div className="py-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
        {/* <div>
          <div className="flex items-center justify-between mb-1">
            <p className="text-gray-500">Paymnet Method</p>
            <button className="text-indigo-600">Change</button>
          </div>
          <div className="border border-gray-300 rounded p-3 flex items-center gap-3">
            <Image
              src={"/assets/all-images/bkash-logo.png"}
              alt=""
              width={50}
              height={50}
              className="min-w-16 lg:min-w-20"
            />

            <div>
              <p className="text-gray-700">0178434*****</p>
              <h3 className="text-gray-700">Mr John Doe</h3>
              <p className="text-sm text-gray-500">Expire in: Nov 2025</p>
            </div>
          </div>
        </div> */}

      </div>
        <p className="text-center text-gray-600">No mathod added yet.</p>

      {/* <div className="w-full overflow-y-auto">
        <h2 className="text-xl font-semibold mb-2 text-indigo-700">
          {t('profile.purchase.list')}
        </h2>
        <div className="max-h-72 overflow-y-auto">
          <table className="table-auto border-collapse w-full">
            <thead>
              <tr className="bg-indigo-600 text-white">
                <th className="min-w-40 px-5 py-3 text-start">{t('profile.ebook.name')}</th>
                <th className="min-w-40 px-4 py-3">{t('profile.author')}</th>
                <th className="min-w-40 px-4 py-3">{t('profile.purchase.date')}</th>
                <th className="min-w-40 px-4 py-3">{t('profile.invoice.no')}</th>
                <th className="min-w-40 px-4 py-3">{t('profile.amount')}</th>
                <th className="min-w-40 px-4 py-3">{t('profile.download.pdf')}</th>
              </tr>
            </thead>
            <tbody className="text-center text-gray-600 w-full">
              <tr className="border-t">
                <td className="px-5 py-3 flex items-center gap-2">
                  <Image
                    src={"/assets/all-images/book-1.jpg"}
                    alt=""
                    width={50}
                    height={30}
                    className="max-h-16"
                  />
                  <p>Ebook Name</p>
                </td>
                <td className=" px-4 py-2">Author</td>
                <td className=" px-4 py-2">Purchase Date</td>
                <td className=" px-4 py-2">Invoice No</td>
                <td className=" px-4 py-2">৳ 20</td>
                <td className=" px-4 py-2">
                  <a href="#" className="text-blue-500 hover:text-blue-700">
                    file.pdf
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div> */}
    </div>
  );
};

export default BillingInfo;
