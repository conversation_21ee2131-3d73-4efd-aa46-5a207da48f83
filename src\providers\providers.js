'use client';

import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { QueryClientProvider } from '@tanstack/react-query';
import { persistor, makeStore } from '@/store/store';
import { queryClient } from '@/lib/queryClient';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/lib/i18n';
import "react-loading-skeleton/dist/skeleton.css";
import { Toaster } from 'sonner';
import { useRef, useEffect } from 'react';

function LoadingState() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
    </div>
  );
}

export function Providers({ children }) {
  const storeRef = useRef();

  if(!storeRef.current){
    storeRef.current = makeStore();
  }
  
  const {store, persistor} = storeRef.current;
  
  return (
    <I18nextProvider i18n={i18n}>
      <Provider store={store}>
        <PersistGate loading={<LoadingState />} persistor={persistor}>
          <QueryClientProvider client={queryClient}>
            <Toaster position="top-right" />
            {children}
          </QueryClientProvider>
        </PersistGate>
      </Provider>
    </I18nextProvider>
  );
}