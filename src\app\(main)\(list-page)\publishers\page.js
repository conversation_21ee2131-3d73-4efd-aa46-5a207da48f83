'use client';

import ItemList from "@/components/ItemList";
import React from "react";
import { useSearchParams } from 'next/navigation';
import useDataFetch from "@/hooks/useDataFetch";
import Loading from "@/app/loading";

const Publishers = () => {
  const searchParams = useSearchParams();
  const search = searchParams.get('search');
  const page = searchParams.get('page');

  const { data, isLoading } = useDataFetch({
    queryKey: ["publishers", { search, page }],
    endPoint: "/publishers",
    params: { 
      search,
      page,
      per_page: 20
    }
  });

  // if (isLoading) return <Loading />;

  return (
    <div>
      <ItemList
        endPoint={"/publishers"}
        navigateTitle="All Publishers"
        title="Publishers"
        seeDetailTitle={"See all publishers"}
        detailLink={"/publisher/"}
        data={data}
        isLoading={isLoading}
      />
    </div>
  );
};

export default Publishers;
