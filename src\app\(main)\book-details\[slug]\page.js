'use client';

import BookDetails from "@/components/book-details/BookDetails";
import useDataFetch from "@/hooks/useDataFetch";
import Loading from "@/app/loading";
import { useParams } from "next/navigation";
import Skeleton from "react-loading-skeleton";

const BookDetailsPage = () => {
  const { slug } = useParams();

  const { data, isLoading, error } = useDataFetch({
    queryKey: ["book-details", slug],
    endPoint: `/ebooks/${slug}`,
  });

  if (isLoading) {
    return (
      <div className="flex items-start justify-center">
        <div className="w-full max-w-7xl mx-auto mt-8 p-5 2xl:px-0 flex gap-6">
          {/* Left Side (Book Image) */}
          <div className="w-52 h-72">
            <Skeleton height="100%" width="100%" borderRadius={8} />
          </div>

          {/* Right Side (Book Details) */}
          <div className="flex-1">
            {/* Title */}
            <Skeleton height={25} width="60%" className="mb-2" />

            {/* Author, Categories & Published Date */}
            <Skeleton height={15} width="50%" className="mb-1" />
            <Skeleton height={15} width="40%" className="mb-1" />
            <Skeleton height={15} width="30%" className="mb-4" />

            {/* Price Box */}
            <div className="w-64 p-4 border rounded-lg">
              <Skeleton height={40} width="100%" />
            </div>

            {/* Buttons */}
            <div className="mt-4 flex gap-4">
              <Skeleton height={40} width="140px" />
              <Skeleton height={40} width="140px" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen flex items-center justify-center text-red-500">
        {error?.message}
      </div>
    );
  }

  return (
    <div>
      <BookDetails bookDetails={data} />
    </div>
  );
};

export default BookDetailsPage;
