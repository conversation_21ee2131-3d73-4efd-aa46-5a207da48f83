"use client";

import React, { useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useRouter, useSearchParams } from "next/navigation";
import * as Yup from "yup";
import { Form, Formik } from "formik";
import FormInput from "@/components/form/FormInput";
import api from "@/lib/api";
import { useDispatch } from "react-redux";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { loginSuccess } from "@/store/features/authSlice";

const Register = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const searchParams = useSearchParams();
  const returnTo = searchParams.get("returnTo") || "/";
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch();

  
const registerSchema = Yup.object().shape({
  name: Yup.string().required(t('auth.required')),
  email: Yup.string().email(t('auth.sign.up.invalid.email')).required(t('auth.required')),
  password: Yup.string()
    .min(8, t('auth.sign.up.atleast.8'))
    .required(t('auth.required')),
  password_confirmation: Yup.string()
    .oneOf([Yup.ref("password"), null], t('auth.sign.up.password.not.match'))
    .required(t('auth.required')),
});

  const handleRegister = async (values) => {
    try {
      setIsLoading(true);
      const response = await api.post("/register", values);
      if (response?.success) {
        dispatch(loginSuccess({
          token: response.token,
          user: response.user
        }));
      
        router.push(returnTo);
      }
    } catch (error) {
      console.log(error);
      setError(error?.response?.data?.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <div className="space-y-2">
        <h2 className="text-2xl text-center text-indigo-600">{t('auth.sign.up')}</h2>
        <p className="text-gray-500 text-sm text-center">
          {t('auth.sign.up.title')}
        </p>
      </div>
      <Formik
        initialValues={{
          email: "",
          password: "",
          password_confirmation: "",
          name: "",
        }}
        validationSchema={registerSchema}
        onSubmit={handleRegister}
      >
        {({ errors }) => (
          <Form className="mt-4 space-y-4">
            {error && (
              <div className="text-red-500 text-center">
                {error?.response?.data?.message}
              </div>
            )}
            <FormInput
              label={t('auth.sign.up.name')}
              name="name"
              type="text"
              placeholder={t('auth.sign.up.name.placeholder')}
            />
            <FormInput
              label={t('auth.sign.up.email')}
              name="email"
              type="email"
              placeholder={t('auth.sign.up.email.placeholder')}
            />
            <div className="relative">
              <FormInput
                label={t('auth.sign.up.password')}
                name="password"
                type={showPassword ? "text" : "password"}
                placeholder={t('auth.sign.up.password.placeholder')}
              />
              <Icon
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-9 text-gray-600 cursor-pointer"
                icon={
                  showPassword ? "iconamoon:eye-off-light" : "clarity:eye-line"
                }
                width="18"
                height="18"
              />
            </div>

            <FormInput
              label={t('auth.sign.up.confirm.password')}
              name="password_confirmation"
              type={showPassword ? "text" : "password"}
              placeholder={t('auth.sign.up.confirm.password.placeholder')}
            />

            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isLoading ? "Processing..." : t('auth.sign.up')}
            </button>
          </Form>
        )}
      </Formik>

      <div className="mt-6 text-center text-sm">
        <p className="text-gray-600">
          {t("auth.have.account")}{" "}
          <Link
            href={`/login?${searchParams}`}
            className="font-medium text-indigo-600 hover:text-indigo-500"
          >
            {t("auth.login.title")}
          </Link>
        </p>
      </div>
    </div>
  );
};

export default Register;
