import axios from 'axios';
import Cookies from 'js-cookie';
import { toast } from 'sonner';
import { API_URL } from '@/config';

// Base Axios instance
const api = axios.create({
  baseURL: API_URL,
  // timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request Interceptor
api.interceptors.request.use(
  (config) => {
    // Get the token from cookies
    const token = Cookies.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    toast.error('Request failed. Please try again.');
    return Promise.reject(error);
  }
);

// Response Interceptor for Success
api.interceptors.response.use(
  (response) => {
    if (response?.config?.showSuccessMessage) {
      toast.success(response.config.successMessage || 'Operation completed successfully!');
    }
    return response.data; // Simplify response handling
  },
  (error) => {
    const errorMessage = error.response?.data?.message || 'Something went wrong!';
    toast.error(errorMessage);
    return Promise.reject(error);
  }
);

export default api;
