'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import Cookies from 'js-cookie';

const ProtectedRoute = ({ children }) => {
  const router = useRouter();
  const { isAuth } = useSelector((state) => state.auth);
  const token = Cookies.get('token');

  useEffect(() => {
    if (!isAuth || !token) {
      router.replace(`/login?returnTo=${window.location.pathname}`);
    }
  }, [isAuth, token, router]);

  if (!isAuth || !token) {
    return null;
  }

  return children;
};

export default ProtectedRoute;