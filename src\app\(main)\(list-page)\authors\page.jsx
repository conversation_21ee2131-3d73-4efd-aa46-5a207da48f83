import ItemList from "@/components/ItemList";
import { Suspense } from "react";


export const metadata = {
  title: "Your-EPub | Author List",
  icons: {
    icon: "/assets/all-images/favlogo.png",
  },
};

const AuthorList = async ({ searchParams }) => {
  const { search, page } = await searchParams;

  async function getData() {
    const queryParams = new URLSearchParams({
      ...(search && { search }),
      ...(page && { page }),
    }).toString();

    const endpoint = `/authors/${queryParams ? `?${queryParams}&per_page=20` : "?per_page=20"}`;
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api${endpoint}`,
      {
        cache: "no-store",
      }
    );
    
    if (!res.ok) {
      throw new Error("Failed to fetch data");
    }
    return res.json();
  }

  const data = await getData();

  return (
    <Suspense fallback={
      <ItemList
        endPoint={"/authors"}
        navigateTitle="All Authors"
        title="Authors"
        seeDetailTitle={"See all publishers"}
        detailLink={"/author/"}
        data={[]}
        isLoading={true}
      />
    }>
      <ItemList
        endPoint={"/authors"}
        navigateTitle="All Authors"
        title="Authors"
        seeDetailTitle={"See all publishers"}
        detailLink={"/author/"}
        data={data?.data}
        isLoading={false}
      />
    </Suspense>
  );
};

export default AuthorList;
