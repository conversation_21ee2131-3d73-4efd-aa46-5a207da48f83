"use client";
import { useAppDispatch, useAppSelector } from "@/hooks/reduxHooks";
import api from "@/lib/api";
import { loginSuccess } from "@/store/features/authSlice";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

const ProfileInfoItem = ({
  title,
  data = "Demo",
  icon,
  inputName,
  className,
  inputType,
  inputPlaceholder,
  textStyle,
  options,
}) => {
  const { t } = useTranslation();
  const [edit, setEdit] = useState(false);
  const [inputValue, setInputValue] = useState(data ? data : "");
  const [submitLoading, setSubmitLoading] = useState(false);
  const [error, setError] = useState(""); // State for validation errors
  const queryClient = useQueryClient();
  const { token, user } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();

  const handleClose = () => {
    setEdit(false);
    setInputValue(data);
    setError(""); // Reset error
  };

  const handleInputChange = (e) => {
    const currentValue = e.target.value;
    setInputValue(currentValue);
    setError(""); // Clear error on input change
  };

  const validateInput = () => {
    if (!inputValue) {
      return `${title} is required.`;
    }
    if (inputType === "email" && !/^\S+@\S+\.\S+$/.test(inputValue)) {
      return "Invalid email address.";
    }
    if (inputType === "number" && isNaN(inputValue)) {
      return `${title} must be a number.`;
    }
    return ""; // No errors
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validationError = validateInput();
    if (validationError) {
      setError(validationError);
      return;
    }

    setSubmitLoading(true);

    let userData = {};

    if (inputName === "password") {
      userData = {
        current_password: e.target.current_password.value, // Get from input field
        new_password: e.target.new_password.value,
      };
    } else {
      userData = { [inputName]: inputValue };
    }

    try {
      await api.post("/profile/update", userData).then((response) => {
        if (response?.data?.id) {
          if (inputName === "password") {
            setInputValue("********");
          } else {
            setInputValue(response?.data[inputName]);
          }

          dispatch(
            loginSuccess({
              user: {
                ...user,
                [inputName]: response?.data[inputName],
              },
              token: token,
            })
          );

          setEdit(false);
          setSubmitLoading(false);
          queryClient.invalidateQueries("/profile");
          toast.success(response?.message);
        }
      });
    } catch (error) {
      console.log(error);
      setSubmitLoading(false);
    } finally {
      setSubmitLoading(false);
    }
  };

  return (
    <div className={className ? className : "flex items-start w-full gap-4"}>
      <Icon icon={icon} className="text-2xl text-sky-600" />
      <div className="w-full">
        <p className="text-base text-gray-700 font-semibold">{title}</p>
        <div className="w-full">
          {edit ? (
            <form
              onSubmit={handleSubmit}
              className={`flex gap-4 mt-2 items-end w-full ${
                inputName === "password" && "flex-col"
              }`}
            >
              {inputType === "textarea" || inputType === "select" || (
                <>
                  {inputName === "password" ? (
                    <>
                      <div className="flex max-sm:flex-col items-center gap-2 w-full">
                        <input
                          onChange={handleInputChange}
                          defaultValue=""
                          name="current_password"
                          type="password"
                          placeholder={t("profile.enter.current.pass")}
                          className="p-2 border rounded-md px-3 w-full"
                        />
                        <input
                          onChange={handleInputChange}
                          defaultValue={""}
                          name="new_password"
                          type="password"
                          placeholder={t("profile.enter.new.pass")}
                          className="p-2 border rounded-md px-3 w-full"
                        />
                      </div>
                    </>
                  ) : (
                    <input
                      onChange={handleInputChange}
                      defaultValue={
                        inputType === "password" ? "*********" : inputValue
                      }
                      name={inputName}
                      type={inputType}
                      placeholder={inputPlaceholder}
                      className="p-2 border rounded-md px-3 w-full"
                    />
                  )}
                </>
              )}
              {inputType === "textarea" && (
                <textarea
                  name={inputName}
                  className="p-2 border rounded-md px-3 min-h-20 w-full"
                  onChange={handleInputChange}
                  defaultValue={inputValue}
                  placeholder={inputPlaceholder}
                ></textarea>
              )}
              {inputType === "select" && (
                <select
                  name={inputName}
                  defaultValue={inputValue}
                  onChange={handleInputChange}
                  className="p-2 border-2 rounded-lg min-w-48"
                >
                  <option value="" disabled>
                    Select {title}
                  </option>
                  {options?.map((item, idx) => (
                    <option key={idx} value={item.value}>
                      {item.label}
                    </option>
                  ))}
                </select>
              )}
              <div className="flex items-center gap-2">
                <button
                  type="submit"
                  className="px-4 py-2 rounded border border-indigo-600 text-gray-700 min-w-24"
                  onClick={handleClose}
                >
                  {t("cancel")}
                </button>
                <button
                  disabled={submitLoading}
                  className="px-4 py-2 rounded border bg-indigo-600 text-white border border-indigo-600"
                >
                  {submitLoading ? (
                    <Icon
                      icon="svg-spinners:3-dots-bounce"
                      className="text-lg"
                    />
                  ) : (
                    t("save")
                  )}
                </button>
              </div>
            </form>
          ) : (
            <div className="flex items-center justify-between w-full">
              <p className={textStyle ? textStyle : "text-base"}>
                {inputValue}
              </p>

              {inputType !== "email" && (
                <button
                  onClick={() => setEdit(true)}
                  className="border rounded px-4 py-2"
                >
                  {t("edit")}
                </button>
              )}
            </div>
          )}
          {edit && error && (
            <p className="text-red-500 text-sm">{error}</p> // Show validation error
          )}

          {/* : (
            <p
              onClick={() => setEdit(true)}
              className="text-sky-600 flex items-center gap-2 cursor-pointer"
            >
              {inputValue ? "Edit" : "Add"} 
              {title}{" "}
              <Icon icon="ic:outline-edit" className="text-xl" />
            </p>
          ) */}
        </div>
      </div>
    </div>
  );
};

export default ProfileInfoItem;
