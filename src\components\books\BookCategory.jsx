"use client";

import { Icon } from "@iconify/react/dist/iconify.js";
import React, { useEffect, useState } from "react";
import { BookCard } from "./BookCard";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import Loading from "@/app/loading";
import Skeleton from "react-loading-skeleton";

const BookCategory = ({
  books,
  categoryTitle,
  categorySlug,
  isRecent = false,
  loading,
  removeYSpace=false
}) => {
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [limit, setLimit] = useState(5);
  const { t } = useTranslation();

  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
     if (screenWidth < 640) {
      setLimit(2);
    } else if (screenWidth < 768) {
      setLimit(3);
    } else if (screenWidth < 1024) {
      setLimit(4);
    } else if(screenWidth < 1300){
      setLimit(5);
    } else {
      setLimit(6);
    }
  }, [screenWidth]);

  return (
    <div className={`max-w-7xl mx-auto p-5 ${removeYSpace ? "xl:p-0 max-sm:p-0" : " xl:px-0"}`}>
      <div className="flex items-center justify-between border-b-2 py-3 text-gray-600 border-gray-400">
        <h2 className="text-2xl font-semibold">{categoryTitle}</h2>
        <Link
          href={isRecent ? "/category/recent" : `/category/${categorySlug}`}
          className="flex items-center gap-2 hover:text-indigo-500 hover:gap-3 transition-all duration-300"
        >
          {t("see.more")} <Icon icon="ep:right" width="20" height="20" />
        </Link>
      </div>

      {loading ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-5 mt-5">
        {[...Array(limit)].map((_, idx) => (
          <div key={idx} className="p-3 border rounded-lg shadow-md">
            <Skeleton height={180} className="mb-2" /> {/* Book Image */}
            <Skeleton height={20} width="80%" className="mb-1" /> {/* Title */}
            <Skeleton height={15} width="60%" /> {/* Author Name */}
          </div>
        ))}
      </div>
      ) : (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-5 mt-5">
          {books?.slice(0, limit)?.map((book, idx) => (
            <BookCard key={idx} book={book} />
          ))}
        </div>
      )}
    </div>
  );
};

export default BookCategory;
