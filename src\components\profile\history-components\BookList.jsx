import Image from "next/image";
import React from "react";

const BookList = () => {
  return (
    <div>
      <div className="py-4 space-y-3">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="flex items-center flex-col md:flex-row gap-3 border-gray-300">
            <Image
              src="/assets/all-images/book-2.png"
              alt=""
              width={50}
              height={50}
              className="min-w-16 lg:min-w-20 max-h-28 rounded-lg"
            />

            <div className="text-sm space-y-1 text-gray-500">
              <p className="text-lg text-gray-800">
                বইয়ের নাম বইয়ের নাম বইয়ের নাম
              </p>
              <p>Anisul Haque</p>
              <p><PERSON><PERSON></p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default BookList;
