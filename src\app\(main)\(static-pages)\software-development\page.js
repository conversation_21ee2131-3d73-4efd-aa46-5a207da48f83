'use client';

import Loading from "@/app/loading";
import HtmlData from "@/components/form/HtmlData";
import useDataFetch from "@/hooks/useDataFetch";
import React from "react";

const SoftwareDevelopment = () => {
  const { data, isLoading } = useDataFetch({
    queryKey: ["static-pages-software-development"],
    endPoint: "/static-pages/software-development",
  });

  if (isLoading) return <Loading />;

  return (
    <div className="max-w-7xl mx-auto p-5 2xl:px-0 my-3">
      {data?.content && <HtmlData htmlData={data?.content} />}
    </div>
  );
};

export default SoftwareDevelopment;
