import React from "react";
import { Icon } from "@iconify/react";

const Pagination = ({ currentPage, totalPages, onPageChange }) => {
  const pageNumbers = [];
  const maxVisiblePages = 5;

  if (totalPages <= maxVisiblePages) {
    for (let i = 1; i <= totalPages; i++) {
      pageNumbers.push(i);
    }
  } else {
    pageNumbers.push(1);
    if (currentPage > 3) pageNumbers.push("...");
    
    const startPage = Math.max(2, currentPage - 1);
    const endPage = Math.min(totalPages - 1, currentPage + 1);

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    if (currentPage < totalPages - 2) pageNumbers.push("...");
    pageNumbers.push(totalPages);
  }

  return (
    <div className="flex items-center space-x-2">
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={`p-1.5 rounded-md border ${currentPage === 1 ? "bg-gray-200 text-gray-400 cursor-not-allowed" : "hover:bg-gray-100"}`}
      >
        <Icon icon="mdi:chevron-left" width="20" height="20" />
      </button>

      {pageNumbers.map((num, idx) => (
        <button
          key={idx}
          onClick={() => num !== "..." && onPageChange(num)}
          className={`px-3 py-1 rounded-md border ${currentPage === num ? "border-indigo-500 text-indigo-600 font-bold" : "hover:bg-gray-100"}`}
          disabled={num === "..."}
        >
          {num}
        </button>
      ))}

      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={`p-1.5 rounded-md border ${currentPage === totalPages ? "bg-gray-200 text-gray-400 cursor-not-allowed" : "hover:bg-gray-100"}`}
      >
        <Icon icon="mdi:chevron-right" width="20" height="20" />
      </button>
    </div>
  );
};

export default Pagination;
