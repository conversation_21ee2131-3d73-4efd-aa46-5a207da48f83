"use client";

// import useDataFetch from "@/hooks/useDataFetch";
import BookCategories from "@/components/books/BookCategories";
import BookCategory from "@/components/books/BookCategory";
import { useTranslation } from "react-i18next";
import HeroSection from "@/components/HeroSection";
// import { useSelector } from "react-redux";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { Suspense } from "react";
import { useAppSelector } from "@/hooks/reduxHooks";
import useDataFetch from "@/hooks/useDataFetch";

const MainHomeClient = () => {
  const { t } = useTranslation();
  const isAllBooksLoading = false;
  // const allbooks = bookData?.recent
  const { data: allbooks, isLoading } = useDataFetch({
    queryKey: ["ebooks"],
    endPoint: "/ebooks",
  });

  // Fetch categories and authors from Redux store
  const {
    categories,
    authors,
    loading: isCategoryLoading,
  } = useAppSelector((state) => state.commonApi);

  const recentBooks = allbooks?.recent || [];
  const genresBooks = allbooks?.genres || [];

  const genresAvailableBooks = genresBooks?.filter(
    (genre) => genre?.ebooks?.length > 0
  );

  return (
    <Suspense
      fallback={<Skeleton height={30} width={200} count={3} className="my-2" />}
    >
      <HeroSection />

      {/* Recent Books Section */}
      <BookCategory
        categoryTitle={t("recent.books")}
        books={recentBooks}
        isRecent={true}
        loading={isLoading}
      />

      {/* Book Categories Section */}
      {isCategoryLoading ? (
        <Skeleton height={30} width={200} count={3} className="my-2" />
      ) : (
        <BookCategories
          sectionTitle={t("book.categories")}
          categoryName={"category"}
          categories={categories?.data}
        />
      )}

      {genresAvailableBooks.map((genre, index) => (
        <div key={index}>
          {/* Authors Section after the first genre */}
          {index === 1 &&
            (isCategoryLoading ? (
              <Skeleton height={30} width={150} className="my-2" />
            ) : (
              <BookCategories
                sectionTitle={t("authors")}
                categories={authors}
                categoryName={"author"}
              />
            ))}

          {/* Genre Books */}
          <BookCategory
            key={genre?.id}
            books={genre?.ebooks}
            categoryTitle={genre?.name}
            categorySlug={genre?.slug}
            // loading={isAllBooksLoading} // Individual loader for genres
          />
        </div>
      ))}
    </Suspense>
  );
};

export default MainHomeClient;
