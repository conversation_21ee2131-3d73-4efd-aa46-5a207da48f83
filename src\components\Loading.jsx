import React from 'react';
import './Loading.css';
import { BookOpen } from 'lucide-react';
export default function Loading() {
  return (
    <div className="loader-container" style={{ position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
      <div className="relative w-16 h-16">
        <div className="absolute inset-0 border-4 border-[#4841BF]/20 rounded-full"></div>
        <div className="absolute inset-0 border-4 border-transparent border-t-[#4841BF] rounded-full animate-spin"></div>
        <BookOpen className="absolute inset-0 w-8 h-8 m-auto text-[#4841BF]" />
      </div>
    </div>
  );
};