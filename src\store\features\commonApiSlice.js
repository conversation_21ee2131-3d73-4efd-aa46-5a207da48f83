import api from "@/lib/api";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";

const CACHE_DURATION = 60 * 60 * 1000; // 60 minutes in milliseconds

const isCacheValid = (timestamp) => {
  if (!timestamp) return false;
  return Date.now() - timestamp < CACHE_DURATION;
};

export const fetchCategories = createAsyncThunk("navbar/fetchCategories", async (_, { getState }) => {
  const { categories, categoriesTimestamp } = getState().commonApi;
  if (categories && isCacheValid(categoriesTimestamp)) {
    return categories;
  }
  const response = await api.get("/categories", { params: { is_feature: 1 } });
  return response.data;
});

export const fetchAuthors = createAsyncThunk("navbar/fetchAuthors", async (_, { getState }) => {
  const { authors, authorsTimestamp } = getState().commonApi;
  if (authors && isCacheValid(authorsTimestamp)) {
    return authors;
  }
  const response = await api.get("/authors", { params: { pagination: false } });
  return response.data;
});

export const fetchPublishers = createAsyncThunk("navbar/fetchPublishers", async (_, { getState }) => {
  const { publishers, publishersTimestamp } = getState().commonApi;
  if (publishers && isCacheValid(publishersTimestamp)) {
    return publishers;
  }
  const response = await api.get("/publishers", { params: { pagination: false } });
  return response.data;
});

const commonApiSlice = createSlice({
  name: "commonApi",
  initialState: {
    categories: null,
    authors: null,
    publishers: null,
    categoriesTimestamp: null,
    authorsTimestamp: null,
    publishersTimestamp: null,
    loading: false,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.categories = action.payload;
        state.categoriesTimestamp = Date.now();
      })
      .addCase(fetchAuthors.fulfilled, (state, action) => {
        state.authors = action.payload;
        state.authorsTimestamp = Date.now();
      })
      .addCase(fetchPublishers.fulfilled, (state, action) => {
        state.publishers = action.payload;
        state.publishersTimestamp = Date.now();
      });
  },
});

export default commonApiSlice.reducer;
