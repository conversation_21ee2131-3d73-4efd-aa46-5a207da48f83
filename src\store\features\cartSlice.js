import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  isAuthenticated: false,
  cart: [],
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    setInitialState: (state, action) => {
      state.isAuthenticated = action.payload.isAuthenticated;
      state.cart = action.payload.cart;
    },
    addToCart: (state, action) => {
      const existingItem = state.cart.find(item => item?.id === action.payload.id);
      if (existingItem) {
        // existingItem.quantity += 1;
        console.log('already exists')
      } else {
        state.cart.push({ ...action.payload, quantity: 1 });
      }
    },

    increaseQuantity: (state, action) => {
      const updatedCart = state.cart.map(item =>
        item.id === action.payload ? { ...item, quantity: item.quantity + 1 } : item
      );
      state.cart = updatedCart;
    },

    decreaseQuantity: (state, action) => {
      const updatedCart = state.cart
        .map(item =>
          item.id === action.payload
            ? { ...item, quantity: item.quantity > 1 ? item.quantity - 1 : 1 }
            : item
        )
        .filter(item => item.quantity > 0);
      state.cart = updatedCart;
    },

    removeFromCart: (state, action) => {
      const updatedCart = state.cart.filter(item => item.id !== action.payload);
      state.cart = updatedCart;
    },
  },
});

export const { addToCart, increaseQuantity, decreaseQuantity, removeFromCart, setInitialState } = cartSlice.actions;
export default cartSlice.reducer;
