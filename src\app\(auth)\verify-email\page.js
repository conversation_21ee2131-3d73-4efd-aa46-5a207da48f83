"use client";

import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslation } from "react-i18next";
import api from "@/lib/api";
import Link from "next/link";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import FormInput from "@/components/form/FormInput";

export default function LoginPage() {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuth } = useSelector((state) => state.auth);
  //   const returnTo = searchParams.get("returnTo") || "/";

  const LoginSchema = Yup.object().shape({
    email: Yup.string().email(t('auth.sign.up.invalid.email')).required(t('auth.required')),
  });

  if (isAuth) return router.push("/");

  const [message, setMessage] = useState("");

  const handleForgotPass = async (values, { setSubmitting, setErrors }) => {
    try {
      const response = await api.post("/forgot-password", values);
      setMessage(response?.status);
      console.log(response);
    } catch (error) {
      setErrors({
        general:
          error?.response?.data?.message ||
          "Something went wrong while verifying email!",
      });
    }
    setSubmitting(false);
  };

  return (
    <div>
      <h2 className="text-2xl font-bold text-center mb-8">
        {t("auth.verify.email")}
      </h2>

      <Formik
        initialValues={{ email: "" }}
        validationSchema={LoginSchema}
        onSubmit={handleForgotPass}
      >
        {({ isSubmitting, errors }) => (
          <Form className="space-y-4">
            {errors.general ? (
              <div className="text-red-500 text-sm text-center">
                {errors.general}
              </div>
            ) : (
              message?.length > 0 && (
                <div className="text-green-500 text-sm text-center">
                  {message}
                </div>
              )
            )}

            <FormInput
              name="email"
              label={t("profile.email")}
              type="email"
              placeholder={t("profile.write.email")}
            />

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full py-2 px-4 rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 disabled:bg-indigo-400"
            >
              {isSubmitting ? `${t("loading")}...` : t('auth.verify')}
            </button>
          </Form>
        )}
      </Formik>

      <div className="mt-6 text-center text-sm">
        <p className="text-gray-600">
          {t("auth.no.account")}{" "}
          <Link
            href={`/register?${searchParams}`}
            className="font-medium text-indigo-600 hover:text-indigo-500"
          >
            {t("register")}
          </Link>
        </p>
      </div>
    </div>
  );
}
