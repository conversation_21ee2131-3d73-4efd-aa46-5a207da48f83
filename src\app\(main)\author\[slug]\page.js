'use client';

import React from "react";
import Books from "@/components/books/Books";
import useDataFetch from "@/hooks/useDataFetch";
import Loading from "@/app/loading";
import { useParams, useSearchParams } from "next/navigation";

const AuthorBooks = () => {
  const { slug } = useParams();
  const searchParams = useSearchParams();
  const authors = searchParams.get('authors');
  const category = searchParams.get('category');
  const rating = searchParams.get('rating');

  const { data, isLoading } = useDataFetch({
    queryKey: ["author-books", slug, { authors, category, rating }],
    endPoint: `/author-books/${slug}`,
    params: { authors, category, rating }
  });

  if (isLoading) return <Loading />;

  return (
    <div>
      <Books
        endPoint={`/author-books/${slug}`}
        showAuthorFilter={false}
        showCategoryFilter={true}
        data={data}
      />
    </div>
  );
};

export default AuthorBooks;
