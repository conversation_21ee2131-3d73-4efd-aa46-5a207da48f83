import React from "react";
import Books from "@/components/books/Books";

const AuthorBooks = async ({ params, searchParams }) => {
  const { slug } = await params;
  const { authors, category, rating } = await searchParams;
  let isLoading = true;

  async function getData() {
    try {
      const queryParams = new URLSearchParams({
        ...(authors && { authors }),
        ...(category && { category }),
        ...(rating && { rating })
      }).toString();

      const endpoint = `/publisher-books/${slug}${queryParams ? `?${queryParams}` : ''}`;
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api${endpoint}`,
        {
          cache: "no-store",
        }
      );
      if (!res.ok) {
        throw new Error("Failed to fetch data");
      }
      return res.json();
    } finally {
      isLoading = false;
    }
  }

  const data = await getData();
  
  return (
    <div>
      <Books
        endPoint={`/publisher-books/${slug}`}
        showAuthorFilter={false}
        showCategoryFilter={true}
        data={data?.data}
      />
    </div>
  );
};

export default AuthorBooks;
