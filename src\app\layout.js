'use client';

import { <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { Providers } from "@/providers/providers";

// const roboto = Roboto({
//   subsets: ["latin"],
//   weight: ["100", "300", "400", "500", "700", "900"],
//   variable: "--font-roboto",
// });

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <title>Your-EPub</title>
        <link rel="icon" href="/assets/all-images/favlogo.png" />
      </head>
      <body>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
