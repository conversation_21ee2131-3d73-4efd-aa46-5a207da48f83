import { useState } from "react";
import useDataFetch from "@/hooks/useDataFetch";
import { Icon } from "@iconify/react";
import Image from "next/image";
import Link from "next/link";
import Pagination from "@/components/Pagination";
import Loading from "@/app/loading";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import Skeleton from "react-loading-skeleton";

const Orders = () => {
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(1);
  const { cart } = useSelector((state) => state.cart);
  const { mainProfileActiveTab } = useSelector((state) => state.profile);
  const { data, isLoading, error } = useDataFetch({
    queryKey: ["get-order-list", currentPage, mainProfileActiveTab],
    endPoint: `/get-orders?page=${currentPage}`,
  });

  const orderList = data?.data || [];
  const totalPages = data?.total_pages || 1;

  const basePrice = cart.reduce(
    (total, book) =>
      total + (book?.price ? parseFloat(book.price) * book?.quantity : 0),
    0
  );

  const totalDiscount = cart.reduce((total, book) => {
    const discount = book?.discount || 0;
    const itemPrice = parseFloat(book?.final_price || book?.price);
    const itemDiscount = (itemPrice * discount) / 100;
    return total + itemDiscount * book?.quantity;
  }, 0);

  const totalPrice = basePrice - totalDiscount;

  const handlePrevPage = () => {
    if (currentPage > 1) setCurrentPage((prev) => prev - 1);
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) setCurrentPage((prev) => prev + 1);
  };

  return (
    <div>
      <div className="flex items-center justify-between w-full pb-5 border-b border-gray-300">
        <h2 className="text-xl font-semibold text-indigo-600">
          {t("profile.my.orders")}
        </h2>
      </div>
      {isLoading ? (
        <div className=" flex items-start justify-center">
          <div className="w-full max-w-7xl mx-auto p-5 2xl:px-0 flex gap-6">
            {/* Left Side (Book Image) */}
            <div className="w-32 h-36">
              <Skeleton height="100%" width="100%" borderRadius={8} />
            </div>

            {/* Right Side (Book Details) */}
            <div className="flex-1">
              {/* Title */}
              <Skeleton height={40} width="60%" className="mb-2" />

              {/* Author, Categories & Published Date */}
              <Skeleton height={25} width="50%" className="mb-1" />
              <Skeleton height={25} width="40%" className="mb-1" />
              <Skeleton height={25} width="30%" className="mb-4" />
            </div>
          </div>
        </div>
      ) : (
        <div className="text-gray-700 pt-3">
          {cart?.length > 0 && (
            <div className="border border-indigo-600 rounded-lg p-4 px-5 w-full flex flex-col sm:flex-row max-sm:items-start gap-3 items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="block bg-[#E1DFFF] p-4 rounded-lg">
                  <Icon
                    icon="emojione-monotone:shopping-cart"
                    width="30"
                    height="30"
                    className="text-blue-600"
                  />
                </span>
                <div>
                  <h2>
                    {t("profile.in.your.cart")} {cart?.length}{" "}
                    {t("profile.items.selected")}
                  </h2>
                  <p>
                    {t("total")}: ৳ {totalPrice}
                  </p>
                </div>
              </div>
              <Link
                href={"cart-details"}
                className="px-4 py-1.5 bg-indigo-600 text-white rounded-md"
              >
                <Icon
                  icon="mdi:cart-outline"
                  width="16"
                  height="16"
                  className="inline mb-1"
                />{" "}
                {t("profile.go.to.cart")}
              </Link>
            </div>
          )}

          <div className="py-1">
            {orderList?.length > 0 ? (
              orderList?.map((order, idx) => (
                <div key={idx} className=" py-2 border-b border-gray-300">
                  <div className="flex items-center justify-between">
                    <p className="text-base">
                      {t("profile.your.order.id")}:{" "}
                      <span className="text-indigo-500">
                        {order?.tracking_number}
                      </span>
                      <span className="text-sm">
                        {" "}
                        ({order?.order_items?.length} Items)
                      </span>
                    </p>
                    <div className="flex items-center gap-3">
                      <button
                        className={`border border-indigo-600 text-indigo-600 rounded px-4 py-1 ${
                          order?.status === "failed" &&
                          "border-red-400 text-red-400"
                        }`}
                      >
                        <Icon
                          icon={
                            order?.status === "failed"
                              ? "mdi:cancel"
                              : "mdi:timer-sand-complete"
                          }
                          width="16"
                          height="16"
                          className="inline mb-0.5"
                        />{" "}
                        {order?.status}
                      </button>
                      {/* <button className="border border-red-500 text-red-500 rounded px-4 py-1">
                  <Icon
                    icon="bitcoin-icons:cross-filled"
                    width="16"
                    height="16"
                    className="inline"
                  />{" "}
                  Cancel
                </button> */}
                    </div>
                  </div>
                  <div className="flex items-center flex-wrap gap-5">
                    {order?.order_items?.map((item, idx) => (
                      <div key={idx} className="my-2">
                        <Image
                          src={
                            item?.item?.cover_image_url
                              ? item?.item?.cover_image_url
                              : "/assets/all-images/book-2.png"
                          }
                          alt=""
                          width={100}
                          height={100}
                          className="min-w-16 max-w-20 object-cover object-top h-28 rounded"
                        />
                        <div className="text-base mt-2">
                          <p>
                            {item?.title?.length > 0
                              ? item?.title?.slice(0, 20) + "..."
                              : item?.title}
                          </p>
                          <p>TK. {item?.subtotal}</p>

                          {/* <p>
                      Payment Amount:{" "}
                      <span className="text-green-500">{item?.subtotal}</span> Taka
                    </p> */}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            ) : (
              <p className="text-center min-h-28 flex items-center justify-center">{t("no.order.found")}</p>
            )}
          </div>


          {totalPages > 1 && (
            <div className="mt-4 flex justify-center">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Orders;
