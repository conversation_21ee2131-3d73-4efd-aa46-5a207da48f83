import { Icon } from "@iconify/react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import useDataFetch from "@/hooks/useDataFetch";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";

const NavSearch = ({ isForSmallScreen }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [isFocused, setIsFocused] = useState(false);

  const { data, isLoading } = useDataFetch({
    queryKey: ["search", searchTerm],
    endPoint: "/search",
    params: { search: searchTerm },
    enabled: searchTerm.length > 1,
  });

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  return (
    <div className="relative lg:w-[42%]">
      <form
        className={`${
          !isForSmallScreen ? "hidden lg:flex" : "flex"
        } items-center group border rounded-md overflow-hidden w-full focus-within:border-indigo-500 focus-within:bg-indigo-500`}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setTimeout(() => setIsFocused(false), 300)} // Delay to allow clicking links
        onSubmit={(e) => (
          e.preventDefault(), router.push(`/search/${searchTerm}`)
        )}
      >
        <input
          type="text"
          placeholder={t("nav.search.text")}
          className="w-full px-3 py-2 border-0 outline-0"
          value={searchTerm}
          onChange={handleSearch}
        />
        <button type="submit" className="px-4">
          <Icon
            icon="cuida:search-outline"
            className="text-xl text-gray-400 group-focus-within:text-white"
          />
        </button>

        {isFocused &&
          searchTerm.length > 2 &&
          (isLoading ? (
            <div className="absolute top-12 left-0 w-full bg-white shadow-lg z-10 rounded-md">
              <div className="px-4 py-2">Loading...</div>
            </div>
          ) : data?.data?.length > 0 ? (
            <div className="absolute top-12 sm:top-14 left-0  bg-white shadow-lg z-50 max-h-72 w-full rounded-md overflow-y-auto space-y-1">
              {data?.data?.map((item) => (
                <Link
                  key={item.id}
                  href={`/book-details/${item?.slug}`}
                  className="flex items-center justify-between gap-3 px-3 py-2 hover:bg-gray-100 bg-white"
                  onClick={(e) => {
                    e.preventDefault();
                    router.push(`/book-details/${item?.slug}`);
                  }}
                >
                  <div className="flex items-center gap-2">
                    <div>
                      <Image
                        src={
                          item?.cover_image
                            ? process.env.NEXT_PUBLIC_BASE_URL +
                              "/storage/" +
                              item?.cover_image
                            : "/assets/all-images/book-1.jpg"
                        }
                        alt={item.title}
                        width={60}
                        height={100}
                        className="min-w-10 max-h-16 rounded"
                      />
                    </div>
                    <div className=" text-start">
                      <span className="block max-w-60">
                        {item?.title?.length > 30
                          ? item?.title?.slice(0, 30) + "..."
                          : item?.title}
                      </span>
                      <span className="text-sm text-gray-500">
                        {item?.authors
                          ?.map((author) => author?.name)
                          .join(" | ") || "Unknown"}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <span className="px-3 py-1 rounded-full bg-indigo-400 text-white">
                      {isNaN(parseInt(item?.discount))
                        ? 0
                        : parseInt(item?.discount)}
                      % {t("discount")}
                    </span>
                    {console.log(item?.final_price)}
                    <span className="block px-4 py-2 font-bold text-lg text-gray-800">
                      ৳{" "}
                      {isNaN(parseInt(item?.final_price))
                        ? 0
                        : parseInt(item?.final_price)}
                    </span>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="absolute top-12 sm:top-14 left-0  bg-white shadow-lg z-50 w-full rounded-md px-4 py-2">No results found</div>
          ))}
      </form>
    </div>
  );
};

export default NavSearch;
