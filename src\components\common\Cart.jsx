"use client";

import { Icon } from "@iconify/react/dist/iconify.js";
import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  decreaseQuantity,
  increaseQuantity,
  removeFromCart,
} from "@/store/features/cartSlice";
import Image from "next/image";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import useSyncCart from "@/hooks/useSyncCart";

const Cart = () => {
  const [showCart, setShowCart] = useState(false);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { isAuth } = useSelector((state) => state.auth);
  const { cart } = useSelector((state) => state.cart);
  const cartData = cart || [];
  // Automatically sync the cart if the user is authenticated
  useSyncCart(cartData, isAuth);
  // Calculate pricing details
  const basePrice = cartData.reduce(
    (total, book) =>
      total + (book?.price ? parseFloat(book.price) * book?.quantity : 0),
    0
  );
  const regularPrice = cartData.reduce(
    (total, book) =>
      total + (book?.price ? parseFloat(book.price) * book?.quantity : 0),
    0
  );
  const totalDiscount = cartData.reduce((total, book) => {
    const discount = book?.discount || 0;
    const itemPrice = parseFloat(book?.final_price || book?.price);
    const itemDiscount = (itemPrice * discount) / 100;
    return total + itemDiscount * book?.quantity;
  }, 0);
  const totalPrice = basePrice - totalDiscount;
  const savings = regularPrice - totalPrice;

  const handleIncreaseQuantity = (book) => {
    dispatch(increaseQuantity(book.id));
  };

  const handleDecreaseQuantity = (book) => {
    if (book?.quantity > 1) {
      dispatch(decreaseQuantity(book.id));
    } else {
      console.log("Quantity cannot be decreased further");
    }
  };

  const handleDeleteFromCart = (id) => {
    dispatch(removeFromCart(id));
  };

  return (
    <div className="relative">
      {showCart ? (
        <div
          className={`fixed right-0 ${
            showCart ? "translate-x-0" : "-translate-x-full"
          } transition duration-1000 ease-in-out max-h-[90vh] min-w-[300px] max-w-[350px] top-1/2 -translate-y-1/2 border transform max-sm:mt-[62px] mt-8 bg-white rounded-lg z-20 shadow-lg`}
        >
          <div className="text-white flex items-center justify-between bg-[#243269] px-3 py-2.5 rounded-tl-lg">
            <Icon
              onClick={() => setShowCart(false)}
              className="absolute p-1 cursor-pointer"
              icon="akar-icons:cross"
              width="24"
              height="24"
            />
            <h2 className="text-lg w-full text-center">
              {t("your.shopping.cart")}
            </h2>
          </div>
          <div className="p-3 h-full">
            <div className="max-h-96 min-h-96 overflow-y-auto mt-2 space-y-2 pr-3">
              {isAuth ? (
                cart.length > 0 ? (
                  cart.map((book, idx) => (
                    <div
                      key={idx}
                      className="border border-gray-300 rounded-lg flex items-center p-3 py-2 gap-3"
                    >
                      <Image
                        className="max-w-16 max-h-24 rounded"
                        height={80}
                        width={200}
                        src={
                          book?.cover_image
                            ? process.env.NEXT_PUBLIC_BASE_URL +
                              "/storage/" +
                              book?.cover_image
                            : "/assets/all-images/book-2.png"
                        }
                        alt={book?.title || "Book Cover"}
                      />
                      <div className="space-y-1 text-gray-600 w-full">
                        <h2 className="text-base text-gray-700">
                          {book?.title?.length > 25
                            ? book?.title?.slice(0, 25) + "..."
                            : book?.title}
                        </h2>
                        <p className="text-sm">
                          {book?.author ? book?.author : t("unknown")}
                        </p>
                        <div className="flex items-center gap-2 text-sm">
                          <span className="flex items-center font-semibold text-gray-800">
                            <Icon
                              icon="mdi:currency-bdt"
                              width="16"
                              height="16"
                            />{" "}
                            {book?.final_price
                              ? parseFloat(book.final_price).toFixed(2)
                              : "0.00"}
                          </span>
                          <span className="flex items-center font-semibold line-through text-gray-400">
                            <Icon
                              icon="mdi:currency-bdt"
                              width="16"
                              height="16"
                            />{" "}
                            {book?.price
                              ? parseFloat(book.price).toFixed(2)
                              : "0.00"}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          {/* <div className="flex items-center justify-between border border-gray-300 px-2 py-1.5 rounded-md w-28">
                            <button
                              onClick={() => handleDecreaseQuantity(book)}
                              className="px-2 bg-gray-200 text-gray-700 rounded"
                            >
                              -
                            </button>
                            <span>{book?.quantity || 1}</span>
                            <button
                              onClick={() => handleIncreaseQuantity(book)}
                              className="px-2 bg-gray-200 text-gray-700 rounded"
                            >
                              +
                            </button>
                          </div> */}
                          <button
                            onClick={() => handleDeleteFromCart(book.id)}
                            className="text-red-500 ml-2 border p-2 rounded-lg border-gray-300 hover:border-gray-400"
                          >
                            <Icon
                              icon="mdi:trash-can-outline"
                              width="20"
                              height="20"
                            />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-center text-gray-600 py-4">
                    {t("cart.empty")}
                  </p>
                )
              ) : cart.length > 0 ? (
                cart.map((book, idx) => (
                  <div
                    key={idx}
                    className="border border-gray-300 rounded-lg flex items-center p-3 py-2 gap-3"
                  >
                    <Image
                      className="max-w-16 max-h-24 rounded"
                      height={80}
                      width={200}
                      src={
                        book?.cover_image
                          ? process.env.NEXT_PUBLIC_BASE_URL +
                            "/storage/" +
                            book.cover_image
                          : "/assets/all-images/book-2.png"
                      }
                      alt={book?.title || "Book Cover"}
                    />
                    <div className="space-y-1 text-gray-600 w-full">
                      <h2 className="text-base text-gray-700">{book?.title}</h2>
                      <p className="text-sm">
                        {book?.author ? book.author : t("unknown")}
                      </p>
                      <div className="flex items-center gap-2 text-sm">
                        <span className="flex items-center font-semibold text-gray-800">
                          <Icon
                            icon="mdi:currency-bdt"
                            width="16"
                            height="16"
                          />{" "}
                          {book?.final_price
                            ? parseFloat(book.final_price).toFixed(2)
                            : "0.00"}
                        </span>
                        <span className="flex items-center font-semibold line-through text-gray-400">
                          <Icon
                            icon="mdi:currency-bdt"
                            width="16"
                            height="16"
                          />{" "}
                          {book?.price
                            ? parseFloat(book.price).toFixed(2)
                            : "0.00"}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center justify-between border border-gray-300 px-2 py-1.5 rounded-md w-28">
                          <button
                            onClick={() => handleDecreaseQuantity(book)}
                            className="px-2 bg-gray-200 text-gray-700 rounded"
                          >
                            -
                          </button>
                          <span>{book?.quantity || 1}</span>
                          <button
                            onClick={() => handleIncreaseQuantity(book)}
                            className="px-2 bg-gray-200 text-gray-700 rounded"
                          >
                            +
                          </button>
                        </div>
                        <button
                          onClick={() => handleDeleteFromCart(book.id)}
                          className="text-red-500 ml-2 border p-2 rounded-lg border-gray-300 hover:border-gray-400"
                        >
                          <Icon
                            icon="mdi:trash-can-outline"
                            width="20"
                            height="20"
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-center text-gray-600 py-4">
                  {t("cart.empty")}
                </p>
              )}
            </div>

            <div className="border-t border-dashed border-gray-400 mt-5 space-y-2 pt-2">
              <p className="flex items-center justify-between">
                <span>{t("base.price")}: </span>{" "}
                <span className="flex items-center font-semibold text-gray-800">
                  <Icon icon="mdi:currency-bdt" width="16" height="16" />{" "}
                  {basePrice ? basePrice.toFixed(2) : "0.00"}
                </span>
              </p>
              <p className="flex items-center justify-between">
                <span>{t("total.discount")}: </span>{" "}
                <span className="flex items-center font-semibold text-gray-800">
                  <Icon icon="mdi:currency-bdt" width="16" height="16" />{" "}
                  {totalDiscount ? totalDiscount.toFixed(2) : "0.00"}
                </span>
              </p>
              <p className="flex items-center justify-between border-t pt-2">
                <span>{t("total")}: </span>{" "}
                <span className="flex items-center font-semibold text-gray-800">
                  <Icon icon="mdi:currency-bdt" width="16" height="16" />{" "}
                  {totalPrice ? totalPrice.toFixed(2) : "0.00"}
                </span>
              </p>
              {/* <p className="text-green-500 font-semibold text-xs w-full text-center my-2">
                {t("saved.total")}: ৳{savings.toFixed(2)}
              </p> */}
              <Link
                href="cart-details"
                className="block bg-indigo-600 text-white px-6 py-2 text-center rounded-md hover:bg-indigo-700 transition w-full"
              >
                {t("view.details")}
              </Link>
            </div>
          </div>
        </div>
      ) : (
        <button
          onClick={() => setShowCart(true)}
          className="fixed z-20 right-0 top-1/2 transform -translate-y-1/2 cursor-pointer bg-indigo-700 text-white p-3 rounded-l-lg"
        >
          <Icon icon="mdi-light:cart" width="24" height="24" />
          {cart?.length > 0 && (
            <span className="absolute bg-white text-gray-800 p-2 border border-indigo-700 -left-3 shadow-lg top-1/2 -translate-y-1/2 rounded-full inline-flex items-center justify-center w-6 h-6">
              {cart?.length}
            </span>
          )}
        </button>
      )}
    </div>
  );
};

export default Cart;
