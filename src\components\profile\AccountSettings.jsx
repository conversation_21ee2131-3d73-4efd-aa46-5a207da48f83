"use client";

import React, { useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useRouter, useSearchParams } from "next/navigation";
import * as Yup from "yup";
import { Form, Formik } from "formik";
import FormInput from "@/components/form/FormInput";
import { useAuth } from "@/hooks/useAuth";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { setEditProfile } from "@/store/features/profileSlice";
import ProfileInfoItem from "./ProfileInfoItem";
import useDataFetch from "@/hooks/useDataFetch";
import Loading from "@/app/loading";

const accountUpdateSchema = Yup.object().shape({
  name: Yup.string().required("Required"),
  email: Yup.string().email("Invalid email").required("Required"),
  password: Yup.string()
    .min(8, "At least 8 characters needed")
    .required("Required"),
  //   password_confirmation: Yup.string()
  //     .oneOf([Yup.ref("password"), null], "Passwords must match")
  //     .required("Required"),
});
const AccountSettings = ({ profileInfo }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [showPassword, setShowPassword] = useState(false);
  const { editProfile } = useSelector((state) => state.profile);
  const [isLoading, setIsLoading] = useState(false);

  // const {
  //   data: profileInfo,
  //   isLoading: isProfileInfoLoading,
  //   refetch: isProfileInfoFetching,
  // } = useDataFetch({
  //   queryKey: ["profile"],
  //   endPoint: "/profile",
  // });

  // const handleSubmit = async (values) => {
  //   try {
  //     //   setIsLoading(true);
  //     console.log(values);
  //   } catch (error) {
  //     console.log(error);
  //   } finally {
  //     //   setIsLoading(false);
  //   }
  // };

  // if (isProfileInfoLoading) {
  //   return <Loading />;
  // }

  return (
    <div className="">
      <div className="">
        <div className="flex items-center justify-between w-full pb-5 border-b border-gray-300">
          <h2 className="text-xl font-semibold text-indigo-600">
            {t("account.settings")}
          </h2>
        </div>
        <div className="my-2 space-y-5">
          <ProfileInfoItem
            title={t("name")}
            icon={""}
            className="flex max-sm:justify-center items-center"
            data={profileInfo?.name}
            textStyle={
              "text-lg flex items-center gap-2 text-gray-700"
            }
            inputName="name"
            inputType="text"
            inputPlaceholder={t("entar.updated.name")}
          />

          <div className="border-b border-gray-300"></div>

          <ProfileInfoItem
            title={t("profile.contact")}
            icon={""}
            className="flex max-sm:justify-center items-center"
            data={profileInfo?.mobile || t('profile.write.contact')}
            textStyle={
              "text-lg flex items-center gap-2 text-gray-700"
            }
            inputName="mobile"
            inputType="number"
            inputPlaceholder={"+8801xxxxxxxxx"}
          />

          <div className="border-b border-gray-300"></div>

          <ProfileInfoItem
            title={t('profile.email')}
            icon={""}
            className="flex max-sm:justify-center items-center"
            data={profileInfo?.email}
            textStyle={
              "text-lg flex items-center gap-2 text-gray-700"
            }
            inputName="email"
            inputType="email"
            inputPlaceholder={t("entar.updated.name")}
          />

          <div className="border-b border-gray-300"></div>

          {/* <ProfileInfoItem
            title={"Present Address"}
            icon={""}
            className="flex max-sm:justify-center items-center"
            data={"Ideal, Banasree, Dhaka."}
            textStyle={
              "text-xl md:text-xl flex items-center gap-2 text-gray-700"
            }
            inputName="name"
            inputType="number"
            inputPlaceholder={t("entar.updated.name")}
          /> */}

          <ProfileInfoItem
            title={t('profile.bio')}
            icon={""}
            className="flex max-sm:justify-center items-center"
            data={profileInfo?.bio}
            textStyle={
              "text-lg flex items-center gap-2 text-gray-700"
            }
            inputName="bio"
            inputType="textarea"
            inputPlaceholder={t('profile.write.bio')}
          />

          <div className="border-b border-gray-300"></div>

          <ProfileInfoItem
            title={t('profile.password')}
            icon={""}
            className="flex max-sm:justify-center items-center"
            data={profileInfo?.password || "*********"}
            textStyle={
              "text-xl md:text-xl flex items-center gap-2 text-gray-700"
            }
            inputName="password"
            inputType="password"
            inputPlaceholder={"password"}
          />
        </div>
      </div>
    </div>
  );
};

export default AccountSettings;
