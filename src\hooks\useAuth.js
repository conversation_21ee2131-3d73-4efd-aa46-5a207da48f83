import { useSelector, useDispatch } from 'react-redux';
import { logout } from '@/store/features/authSlice';

export function useAuth() {
  const auth = useSelector((state) => state.auth);
  const dispatch = useDispatch();

  const isAuthenticated = !!auth.token;

  const handleLogout = () => {
    dispatch(logout());
  };

  return {
    user: auth.user,
    token: auth.token,
    isLoading: auth.isLoading,
    error: auth.error,
    isAuthenticated,
    logout: handleLogout
  };
}

export default useAuth;