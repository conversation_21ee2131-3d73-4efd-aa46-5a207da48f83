import { useEffect, useRef, useState } from "react";
import api from "@/lib/api";
import debounce from "lodash/debounce";

const useSyncCart = (cart, isAuth) => {
  const prevCartRef = useRef(cart);
  const prevIsAuthRef = useRef(isAuth);
  const [syncResponse, setSyncResponse] = useState(null);

  useEffect(() => {
    if (
      JSON.stringify(prevCartRef.current) !== JSON.stringify(cart) ||
      prevIsAuthRef.current !== isAuth
    ) {
      prevCartRef.current = cart;
      prevIsAuthRef.current = isAuth;

      if (!isAuth) return;

      const cartItems = cart.map((item) => ({
        item_id: item.id,
        quantity: item.quantity,
      }));

      // Debounce the API call to prevent rapid successive requests
      const syncCart = debounce(async () => {
        try {
          const response = await api.post("/sync-cart", { book: cartItems });
          setSyncResponse(response?.data);
          return response?.data;
        } catch (error) {
          console.error("Error syncing cart:", error);
          setSyncResponse(null);
        }
      }, 300);

      syncCart();

      // Cleanup the debounced function on unmount or before next effect call
      return () => {
        syncCart.cancel();
      };
    }
  }, [cart, isAuth]);

  // console.log(syncResponse);

  return syncResponse;
};

export default useSyncCart;

