export const createImage = (url) =>
  new Promise((resolve, reject) => {
    const image = new window.Image(); // Use the native HTMLImageElement
    image.crossOrigin = "anonymous"; // Prevent CORS issues
    image.src = url;
    image.onload = () => resolve(image);
    image.onerror = (error) => reject(error);
  });

export const getCroppedImg = async (imageSrc, cropAreaPixels) => {
  // Use the adjusted createImage function that works with Next.js
  const image = await createImage(imageSrc);
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");

  // Set canvas size based on cropped area
  canvas.width = cropAreaPixels.width;
  canvas.height = cropAreaPixels.height;

  // Draw the image onto the canvas, cropping it based on the area
  ctx.drawImage(
    image,
    cropAreaPixels.x,
    cropAreaPixels.y,
    cropAreaPixels.width,
    cropAreaPixels.height,
    0,
    0,
    cropAreaPixels.width,
    cropAreaPixels.height
  );

  // Return the cropped image as a File object
  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      const croppedFile = new File([blob], "cropped-image.png", {
        type: "image/png",
      });
      resolve(croppedFile);
    }, "image/png");
  });
};
