"use client";

import useDataFetch from "@/hooks/useDataFetch";
import { Icon } from "@iconify/react";
import Image from "next/image";
import Link from "next/link";
// import AccountSettings from "./AccountSettings";
import BillingInfo from "@/components/profile/BillingInfo";
import { useTranslation } from "react-i18next";
import AccountSettings from "@/components/profile/AccountSettings";
import { useEffect, useRef, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setMainProfileActiveTab } from "@/store/features/profileSlice";
import BookList from "@/components/profile/history-components/BookList";
import Orders from "@/components/profile/history-components/Orders";
import BookLibrary from "@/components/profile/history-components/BookLibrary";
import WishList from "@/components/profile/history-components/WishList";
import Reviews from "@/components/profile/history-components/Reviews";
import Loading from "@/app/loading";
import api from "@/lib/api";
import { useQueryClient } from "@tanstack/react-query";
import { loginSuccess } from "@/store/features/authSlice";
import Cropper from "react-easy-crop";
import { getCroppedImg } from "@/hooks/getCroppingImg";
import { toast } from "sonner";

const Profile = () => {
  const { mainProfileActiveTab } = useSelector((state) => state.profile);
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const [loading, setLoading] = useState(false);
  const [showName, setShowName] = useState({});
  const fileInputRef = useRef();
  const [imageFile, setImageFile] = useState(null);
  const [showCropper, setShowCropper] = useState(false);
  const [imgSrc, setImgSrc] = useState("/assets/all-images/profileAvater.png");
  const [croppedImage, setCroppedImage] = useState(null);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);

  const categories = [
    { icon: "mdi:user-outline", name: t("profile.my.account") },
    { icon: "mdi:cart-outline", name: t("profile.orders") },
    { icon: "mdi:library-outline", name: t("profile.ebook.library") },
    { icon: "solar:heart-linear", name: t("profile.wishlist") },
    { icon: "solar:star-line-duotone", name: t("profile.reviews") },
    { icon: "mdi-light:bank", name: t("profile.bkash.account") },
  ];
  const { user, token } = useSelector((state) => state.auth);

  const {
    data: profileInfo,
    isLoading: isProfileInfoLoading,
    refetch: isProfileInfoFetching,
  } = useDataFetch({
    queryKey: ["profile"],
    endPoint: "/profile",
  });

  useEffect(() => {
    if (croppedImage) {
      setImgSrc(URL.createObjectURL(croppedImage));
    } else if (!isProfileInfoLoading && profileInfo?.profile_picture_url) {
      setImgSrc(profileInfo.profile_picture_url);
    } else {
      setImgSrc("/assets/all-images/profileAvater.png");
    }
  }, [croppedImage, isProfileInfoLoading, profileInfo?.profile_picture_url]);

  const handleClearFile = () => {
    setShowName("");
    setShowImagePreview(null);
    fileInputRef.current.value = "";
    setIsEditing(false);
  };

  const data = [];
  const isLoading = false;

  const userData = data?.data;

  if (isLoading) {
    return <Loading />;
  }

  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImageFile(file);
      setShowCropper(true);
      setImgSrc(URL.createObjectURL(file));
    }
  };

  const onCropComplete = useCallback((croppedArea, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const handleSaveImage = async () => {
    if (imageFile && croppedAreaPixels) {
      try {
        setLoading(true);
        const croppedImage = await getCroppedImg(imgSrc, croppedAreaPixels);
        setCroppedImage(croppedImage);

        const formData = new FormData();
        formData.append("profile_picture", croppedImage);

        const response = await api.postForm("/profile/update", formData);
        if (response?.data?.id) {
          queryClient.invalidateQueries("/profile");
          setShowCropper(false);
          setImgSrc(response?.data?.profile_picture_url);
          dispatch(
            loginSuccess({
              user: {
                ...user,
                profile_picture: response?.data?.profile_picture_url,
              },
              token: token,
            })
          );
        }
      } catch (error) {
        console.error("Error uploading image:", error);
        toast.error("Something went wrong while updating profile");
      } finally {
        setLoading(false);
      }
    }
  };

  const handleGotoDetails = (i) => {
    if (i === 0) {
      dispatch(setMainProfileActiveTab(1));
    } else if (i === 1) {
      dispatch(setMainProfileActiveTab(3));
    } else {
      dispatch(setMainProfileActiveTab(3));
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0">
      <div className="flex flex-col gap-10">
        <div className="rounded-lg border shadow-md overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2  bg-[#F5EDFF] relative p-5 md:p-8">
            <div className="col-span-1 flex items-center flex-col sm:flex-row justify-center gap-5">
              <div className="flex relative">
                <div className="relative group">
                  <div className="group-hover:bg-black group-hover:bg-opacity-30 absolute inset-0 rounded-xl"></div>
                  <div className="w-40 h-40">
                    <Image
                      height={500}
                      width={500}
                      src={imgSrc}
                      priority
                      alt={profileInfo?.name + " profile"}
                      onError={() =>
                        setImgSrc("/assets/all-images/profileAvater.png")
                      }
                      className="h-full w-full rounded-xl object-cover border-gray-300"
                    />
                  </div>
                  <input
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="hidden"
                    accept=".jpeg,.jpg,.png"
                    type="file"
                  />{" "}
                  {showCropper && (
                    <div className="fixed z-10 inset-0 flex items-center justify-center bg-black bg-opacity-50">
                      <div className="relative w-96 h-96 bg-white rounded-lg shadow-lg">
                        <Cropper
                          image={imgSrc}
                          crop={crop}
                          zoom={zoom}
                          aspect={1} // Keeps a square image
                          onCropChange={setCrop}
                          onZoomChange={setZoom}
                          onCropComplete={onCropComplete}
                        />
                        <div className="flex justify-between mt-4 absolute gap-4 bottom-3 right-3">
                          <button
                            className="px-4 py-2 bg-gray-200 rounded"
                            onClick={() => setShowCropper(false)}
                          >
                            Cancel
                          </button>
                          <button
                            disabled={loading}
                            className={`px-4 py-2 bg-blue-600 text-white rounded ${
                              loading
                                ? "opacity-50 cursor-not-allowed"
                                : "cursor-pointer"
                            }`}
                            onClick={handleSaveImage}
                          >
                            Save
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 p-1 rounded">
                    <span
                      className="cursor-pointer"
                      onClick={() => fileInputRef.current.click()}
                    >
                      <Icon
                        icon="famicons:camera-sharp"
                        className="text-2xl cursor-pointer text-white lg:hidden group-hover:block"
                      />
                    </span>
                  </div>
                </div>
              </div>

              <div className="w-full text-center sm:text-left">
                <h2 className="text-2xl font-semibold text-indigo-600">
                  {profileInfo?.name || "Unknown"}
                </h2>
                <p className="text-lg text-gray-600">{profileInfo?.email}</p>
                <p className="text-gray-600">{profileInfo?.mobile}</p>
                {profileInfo?.address && (
                  <p className="text-gray-600">
                    <Icon
                      icon="mdi:location"
                      width="20"
                      height="20"
                      className="inline text-red-500 mb-1"
                    />{" "}
                    {profileInfo?.address}
                  </p>
                )}
              </div>
            </div>

            <div className="col-span-1 flex flex-col sm:flex-row sm:flex-wrap m-4 justify-start xl:justify-end items-center gap-5">
              {[...Array(2)].map((_, i) => (
                <div
                  key={i}
                  className={`${
                    i === 0 ? "bg-[#F5FBF2]" : "bg-[#F7F0FB]"
                  } border rounded-lg shadow p-4 relative pt-12 w-full sm:w-auto min-w-[120px] md:min-w-40 hover:shadow-lg`}
                >
                  <Icon
                    className={`absolute right-3 top-3 ${
                      i === 0 ? "text-green-500" : "text-red-600"
                    }`}
                    icon={i === 0 ? "ion:book-outline" : "proicons:heart"}
                    width="30"
                    height="30"
                  />
                  <p>
                    {i === 0
                      ? profileInfo?.order_items_count
                      : profileInfo?.wish_count}
                  </p>
                  <p className="text-sm text-gray-500">
                    {i === 0 ? t("book.purchased") : t("my.wishlist")}
                  </p>
                  <span
                    onClick={() => handleGotoDetails(i)}
                    className="text-gray-700 font-semibold text-sm cursor-pointer"
                  >
                    {t("details")}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-8">
          <div className="col-span-1">
            <div className="col-span-1 border border-indigo-600 p-4 rounded shadow">
              {categories.map((category, index) => (
                <div
                  key={index}
                  className={`py-2 w-full ${
                    mainProfileActiveTab === index
                      ? "text-white bg-indigo-700"
                      : "text-gray-600 hover:bg-indigo-100"
                  } focus:outline-none transition-all duration-100 rounded cursor-pointer`}
                >
                  <div
                    onClick={() => dispatch(setMainProfileActiveTab(index))}
                    className={`px-3 py-1 w-full text-base flex items-center justify-between ${
                      index !== categories.length - 1 && ""
                    } border-gray-300 gap-2`}
                  >
                    <div className="flex items-center gap-2">
                      <Icon icon={category?.icon} height={20} width={20} />
                      <span>{category?.name}</span>
                    </div>
                    <Icon icon="ep:right" className="text-lg" />{" "}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="col-span-1 md:col-span-2 lg:col-span-3 bg-white shadow-lg p-5 rounded-lg">
            {isProfileInfoLoading ? (
              <Loading />
            ) : (
              <div className="">
                {mainProfileActiveTab === 0 && (
                  <AccountSettings profileInfo={profileInfo} />
                )}
                {mainProfileActiveTab === 1 && <Orders />}
                {mainProfileActiveTab === 2 && <BookLibrary />}
                {mainProfileActiveTab === 3 && <WishList />}
                {mainProfileActiveTab === 4 && <Reviews />}
                {mainProfileActiveTab === 5 && <BillingInfo />}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
