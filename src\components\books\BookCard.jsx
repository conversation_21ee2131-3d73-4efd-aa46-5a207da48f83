"use client";

import { useRouter } from "next/navigation";
import { Icon } from "@iconify/react/dist/iconify.js";
import Image from "next/image";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { addToCart } from "@/store/features/cartSlice";
import api from "@/lib/api";
import { setMainProfileActiveTab } from "@/store/features/profileSlice";

export const BookCard = ({ book }) => {
  const { title, cover_image, writer } = book;
  const dispatch = useDispatch();
  const router = useRouter();
  const { cart } = useSelector((state) => state.cart);
  const cartData = Array.isArray(cart) ? cart : [];
  const { isAuth } = useSelector((state) => state.auth);
  // console.log(book)

  const isBookAddedToCart = cartData?.some((item) => item?.id === book?.id);
  const { t } = useTranslation();

  const handleAddToCart = async () => {
    dispatch(addToCart(book));
  };

  const handleBuyNow = (book) => {
    if (book?.have_bought) {
      router.push("/profile");
      dispatch(setMainProfileActiveTab(2));
      return;
    }
    dispatch(addToCart(book));
    router.push("/cart-details");
  };

  const handleGoToDetails = () => {
    router.push(`/book-details/${book?.slug}`);
  };

  // const truncateText = (text, maxLength) =>
  //   text.length > maxLength ? text.slice(0, maxLength) + " ..." : text;

  // const truncateTitle = (title) => {
  //   if (!title) return "";

  //   const maxLength =
  //     window.innerWidth < 640
  //       ? 27
  //       : window.innerWidth < 1024
  //       ? 30
  //       : window.innerWidth < 1300
  //       ? 30
  //       : 50;

  //   return title.length > maxLength ? title.slice(0, maxLength) + "..." : title;
  // };

  // const getAuthors = () => {
  //   const authorsText =
  //     book?.authors?.map((item) => item.name).join(", ") ||
  //     book?.author ||
  //     "--";
  //   if (window.innerWidth < 640) return truncateText(authorsText, 15);
  //   if (window.innerWidth < 1300) return truncateText(authorsText, 16);
  //   return truncateText(authorsText, 23);
  // };

  // const getCategories = () => {
  //   const categoriesText =
  //     book?.categories?.map((item) => item.name).join(", ") ||
  //     book?.category ||
  //     "Others";
  //   if (window.innerWidth < 640) return truncateText(categoriesText, 20);
  //   if (window.innerWidth < 1300) return truncateText(categoriesText, 15);
  //   return truncateText(categoriesText, 19);
  // };    

  return (
    <div className="rounded-lg border border-gray-300 relative group overflow-hidden max-sm:pb-8 sm:pb-10 lg:pb-0">
      {/* Discount Tag */}
      {book?.discount > 0 && (
        <span className="text-sm font-semibold bg-indigo-500 text-white px-3 py-1 absolute top-0 left-0 rounded-tl-lg rounded-br-lg z-10">
          {book?.discount ? parseInt(book?.discount) : 0}% {t("off")}
        </span>
      )}

      <div className="relative cursor-pointer">
        {/* Book Image */}
        <div onClick={handleGoToDetails} className="relative p-2 pb-0 ">
          <Image
            className="w-full h-[200px] xl:h-[230px] object-cover object-top rounded"
            // fill
            src={
              cover_image
                ? process.env.NEXT_PUBLIC_BASE_URL + "/storage/" + cover_image
                : "/assets/all-images/book-1.jpg"
            }
            alt=""
            height={600}
            width={400}
          />
        </div>

        {/* Cart Icon */}
        <span
          onClick={isBookAddedToCart ? undefined : handleAddToCart}
          className={`absolute top-3 right-3 p-2 rounded-lg ${
            isBookAddedToCart
              ? "bg-indigo-500 text-white cursor-not-allowed"
              : "bg-gray-100"
          }`}
        >
          <Icon icon="mdi-light:cart" width="24" height="24" />
        </span>
      </div>

      {/* Book Info */}
      <div
        onClick={handleGoToDetails}
        className="text-gray-500 py-4 p-3 max-sm:p-3 cursor-pointer"
      >
        <h2
          style={{
            display: "-webkit-box",
            overflow: "hidden",
            WebkitLineClamp: 2,
            WebkitBoxOrient: "vertical",
          }}
          className="text-lg font-semibold text-gray-700 h-[54px]"
        >
          {/* {truncateTitle(title)} */}
          {title}
        </h2>
        <div className="pt-2 text-base">
          <p className="overflow-hidden whitespace-nowrap text-ellipsis">
            {book?.authors?.map((item) => item.name).join(", ") || "--"}
          </p>
          {book?.categories?.length > 0 && (
            <p className="overflow-hidden whitespace-nowrap text-ellipsis">
              {book?.categories?.map((item) => item.name).join(", ")}
            </p>
          )}
        </div>

        {/* Price */}
        <div className="flex items-center gap-2">
          <span className="flex items-center font-semibold text-red-500">
            <Icon icon="mdi:currency-bdt" width="20" height="20" />
            {!isNaN(parseFloat(book?.final_price))
              ? parseFloat(book?.final_price)
              : 0}
          </span>
          {parseInt(book?.discount) > 0 && (
            <span className="flex items-center font-semibold line-through">
              <Icon icon="mdi:currency-bdt" width="20" height="20" />
              {!isNaN(parseInt(book?.price)) ? parseInt(book?.price) : 0}
            </span>
          )}
        </div>
      </div>

      {/* Buy Now Button with Smooth Animation */}
      <button
        onClick={() =>
          isBookAddedToCart ? router.push("/cart-details") : handleBuyNow(book)
        }
        className={`bg-indigo-500 hover:bg-indigo-600 text-white py-2 text-center w-full absolute bottom-0 left-0
          transform lg:translate-y-full lg:opacity-0 lg:group-hover:translate-y-0 lg:group-hover:opacity-100
          transition-all duration-300 md:py-3 md:text-sm md:font-semibold cursor-pointer`}
      >
        {isBookAddedToCart
          ? t("profile.go.to.cart")
          : book?.have_bought
          ? "View Book"
          : t("buy.now")}
      </button>
    </div>
  );
};
