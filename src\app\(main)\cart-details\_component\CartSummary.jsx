"use client";
import api from "@/lib/api";
import { Icon } from "@iconify/react";
import Image from "next/image";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

const CartSummary = ({
  basePrice,
  isBkashChecked,
  setIsBkashChecked,
  selectedBooks,
  setShowSuccessModal,
  handleSelectedItemsRemove,
}) => {
  const { t } = useTranslation();
  const [showCouponInput, setShowCouponInput] = useState(true);
  const [couponCode, setCouponCode] = useState("");
  const [couponApplyLoading, setCouponApplyLoading] = useState(false);
  const [couponError, setCouponError] = useState("");
  const [discount, setDiscount] = useState(0);
  const [loading, setLoading] = useState(false);

  const totalPrice = basePrice - basePrice * (discount / 100);
  const totalDiscountAmount = basePrice - totalPrice;

  // coupon apply
  const handleApplyCoupon = async (e) => {
    e.preventDefault();
    const couponCode = e.target.coupon.value;
    setCouponCode(couponCode);
    try {
      setCouponApplyLoading(true);
      const response = await api.post(`/check-coupon`, {
        coupon_code: couponCode,
      });
      toast.success(response?.message);
      setCouponError("");
      setShowCouponInput(false);
      setDiscount(parseFloat(response?.data?.discount_value || 0));
    } catch (error) {
      setCouponError(error?.response?.data?.message);
    } finally {
      setCouponApplyLoading(false);
    }
  };

  const handleCheckout = async () => {
    if (selectedBooks?.length === 0) {
      toast.error("Please select at least one item to proceed with checkout.");
      return;
    }
    try {
      setLoading(true);
      const response = await api.post(`/orders`, {
        items: selectedBooks,
        payment_method: isBkashChecked ? "bkash" : "",
        shipping_fee: 0,
        coupon_code: couponCode,
        notes: "",
      });
      if (response?.data?.payment_url && response?.data?.status === "pending") {
        setIsBkashChecked(true)
        setShowSuccessModal(true);
        handleSelectedItemsRemove();
        // Redirect to payment URL if it exists
        setTimeout(() => {
          window.location.href = response.data.payment_url;
        }, 1000);
      } else if (response?.data?.id) {
        // to show correct modal text
        setIsBkashChecked(false)
        setShowSuccessModal(true);
        handleSelectedItemsRemove();
        // toast.success(response?.message);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="shadow-lg rounded-lg bg-white p-5 text-gray-700">
      <h2 className="text-xl font-semibold mb-4">{t("order.summary")}</h2>
      <div className="space-y-2">
        <div>
          {showCouponInput ? (
            <div>
              <form
                onSubmit={handleApplyCoupon}
                className="mt-4 flex items-center gap-2"
              >
                <input
                  type="text"
                  defaultValue={couponCode}
                  placeholder={t("enter.coupon")}
                  name="coupon"
                  className="w-full border rounded p-2 focus:outline-blue-300"
                />
                <button
                  type="submit"
                  disabled={couponApplyLoading}
                  className={`px-4 bg-indigo-600 text-white py-2 rounded ${
                    couponApplyLoading ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                >
                  {t("apply")}
                </button>
              </form>
              <span className="text-xs text-red-500">{couponError}</span>
            </div>
          ) : (
            <div>
              <button
                onClick={() => setShowCouponInput(true)}
                className="px-4 bg-indigo-200 text-indigo-600 py-1 rounded text-sm"
              >
                {couponCode}{" "}
                <Icon
                  icon="iconamoon:edit"
                  width="15"
                  height="15"
                  className="inline mb-0.5"
                />
              </button>
            </div>
          )}
        </div>
        <div className="flex justify-between">
          <p>{t("subtotal")}</p>
          <p>৳ {basePrice.toFixed(2)}</p>
        </div>
        <div className="border-b border-dashed border-gray-300"></div>
        <div className="flex justify-between">
          <p>{t("discount")}</p>
          <p>৳ {totalDiscountAmount}</p>
        </div>
        <div className="border-b border-dashed border-gray-300"></div>
        <div className="flex justify-between font-semibold">
          <p>{t("total")}</p>
          <p>৳ {totalPrice.toFixed(2)}</p>
        </div>

        <div>
          <label
            htmlFor="bkash_payment"
            className="flex items-center gap-2 cursor-pointer text-xs border px-4 rounded-lg my-3 p-2 shadow-md shadow-[rgba(0,0,0,0.1)_inset_0px_0px_10px]"
            onClick={() => setIsBkashChecked(!isBkashChecked)}
          >
            <input
              type="checkbox"
              className="form-checkbox"
              id="bkash_payment"
              checked={isBkashChecked}
              onChange={() => setIsBkashChecked(!isBkashChecked)}
            />
            <div className="relative">
              <Image
                src={"/assets/all-images/bkash-logo.png"}
                height={40}
                width={70}
                alt="bkash payment method"
                className="max-8"
              />
              <p>{t("bkash.paymnet")}</p>
            </div>
          </label>
        </div>
        {console.log(selectedBooks)}
        <button
          onClick={handleCheckout}
          disabled={selectedBooks?.length === 0 || loading}
          className={`w-full bg-indigo-600 text-white py-2 rounded mt-4 ${
            selectedBooks?.length === 0 || loading ? "opacity-50" : ""
          }`}
        >
          {loading ? "Processing..." : t("procced.to.checkout")}
        </button>
      </div>
    </div>
  );
};

export default CartSummary;
