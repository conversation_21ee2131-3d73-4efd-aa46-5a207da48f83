const { createSlice } = require("@reduxjs/toolkit");

const profileSlice = createSlice({
    name: "profile",
    initialState: {
        mainProfileActiveTab: 0,
        historyActiveCategory: 0,
        editProfile: false,
    },
    reducers: {
        setMainProfileActiveTab(state, action) {
            state.mainProfileActiveTab = action.payload;
        },
        setHistoryActiveCategory(state, action) {
            state.historyActiveCategory = action.payload;
        },
        setEditProfile(state, action) {
            state.editProfile = action.payload;
        },
    }
})

export const { setMainProfileActiveTab, setHistoryActiveCategory, setEditProfile } = profileSlice.actions;
export default profileSlice.reducer;