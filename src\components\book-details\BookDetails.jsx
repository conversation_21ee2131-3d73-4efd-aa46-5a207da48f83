"use client";

import React, { useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import BookReviews from "./BookReviews";
import useDataFetch from "@/hooks/useDataFetch";
import Loading from "@/components/Loading";
import Link from "next/link";
import { useParams, usePathname, useRouter } from "next/navigation";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { addToCart } from "@/store/features/cartSlice";
import api from "@/lib/api";
import { useQueryClient } from "@tanstack/react-query";
import { setMainProfileActiveTab } from "@/store/features/profileSlice";
import Skeleton from "react-loading-skeleton";
import BookDetailTabs from "./BookDetailTabs";
import ReadModal from "./ReadModal";
import BookCategory from "../books/BookCategory";
import { toast } from "sonner";

const BookDetails = ({ bookDetails, isLoading }) => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const router = useRouter();
  const bookPathname = usePathname();
  const dispatch = useDispatch();
  const { isAuth, token } = useSelector((state) => state.auth);
  const [showReadModal, setShowReadModal] = useState(false);
  // const bookDetails = data?.data;
  const { cart } = useSelector((state) => state.cart);
  const cartData = Array.isArray(cart) ? cart : [];
  const isBookAddedToCart = cartData?.some(
    (item) => item?.id === bookDetails?.id
  );

  const handleAddToCart = (book) => {
    // console.log(book);
    dispatch(addToCart(book));
  };

  const handleBuyNow = (book) => {
    if (isBookAddedToCart) {
      router.push("/cart-details");
    } else {
      dispatch(addToCart(book));
      router.push("/cart-details");
    }
  };

  const handleAddToWishlist = async (id) => {
    const response = await api.post(`/wishlists`, { item_id: id });
    if (response?.data?.id) {
      queryClient.invalidateQueries(`/ebooks/${bookDetails?.slug}`);
      toast.success(response?.message);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      // Web Share API (For Mobile & Modern Browsers)
      try {
        await navigator.share({
          title: bookDetails?.title,
          text: `Check out this book: ${bookDetails?.title}`,
          url: "https://yourepub.com" + bookPathname,
        });
        console.log("Book shared successfully!");
      } catch (error) {
        console.error("Error sharing book:", error);
      }
    } else {
      // Fallback: Copy to Clipboard
      try {
        await navigator.clipboard.writeText(
          "https://yourepub.com" + bookPathname
        );
        alert("Book link copied to clipboard!");
      } catch (error) {
        console.error("Failed to copy:", error);
      }
    }
  };

  return (
    <div className="max-w-7xl mx-auto my-5 p-5 2xl:px-0">
      <div>
        <h2
          onClick={() => router.back()}
          className="text-base flex items-center gap-2 hover:gap-3 transition-all duration-300 hover:text-indigo-600 cursor-pointer"
        >
          <Icon icon="mingcute:arrow-left-line" width="24" height="24" />{" "}
          {bookDetails?.title}
        </h2>
      </div>

      <section className="space-y-12">
        <div className="flex xl:items-start justify-between flex-col xl:flex-row gap-3 lg:gap-10 xl:gap-20 my-7">
          <div className="col-span-12 lg:col-span-7 flex flex-col sm:flex-row items-center gap-5 lg:gap-6 xl:w-[60%]">
            <div className="md:flex md:justify-center md:items-center lg:col-span-2 min-w-[230px] max-w-[250px] relative">
              <Image
                src={
                  bookDetails?.cover_image_url
                    ? bookDetails?.cover_image_url
                    : "/assets/all-images/book-2.png"
                }
                className="object-cover object-top w-full h-[350px] rounded-lg"
                alt={bookDetails?.title || 'Book Cover'}
                priority
                height={600}
                width={400}
              />
            </div>

            <div className="w-full text-gray-700 space-y-2">
              <h2 className="text-xl md:text-2xl font-semibold">
                {bookDetails?.title}
              </h2>
              <p className="flex items-center gap-2">
                <span className="font-semibold">{t("writer")}: </span>
                {bookDetails?.authors?.map((item) => item.name).join(", ") ||
                  bookDetails?.author ||
                  "Unknown"}
              </p>
              <p className="flex items-center gap-2">
                <span className="font-semibold">{t("category")}: </span>{" "}
                {bookDetails?.categories?.map((item) => item.name).join(", ") ||
                  bookDetails?.category ||
                  "Others"}
              </p>
              {bookDetails?.publisher && (
                <p className="flex items-center gap-2">
                  <span className="font-semibold">{t("prokashoni")}: </span>
                  {bookDetails?.publisher}
                </p>
              )}
              <p className="flex items-center gap-2">
                <span className="font-semibold">{t("last.update")}: </span>১ম
                প্রকাশিত, ২০২০
              </p>

              {bookDetails?.format === ("ebook" || "both") && (
                <div className="border border-indigo-500 bg-indigo-50 flex max-sm:flex-col w-full items-center rounded-lg p-3">
                  <div className="relative">
                    <Image
                      className="w-20 max-h-[90px] rounded"
                      src={
                        bookDetails?.cover_image_url
                          ? bookDetails?.cover_image_url
                          : "/assets/all-images/book-2.png"
                      }
                      alt={bookDetails?.title}
                      height={400}
                      width={200}
                    />
                  </div>
                  <div className="w-full text-gray-600 mt-2 sm:mt-0 sm:ml-3">
                    <p>{t("buy.ebook")}</p>
                    <span className="flex items-center font-semibold text-red-500">
                      <Icon icon="mdi:currency-bdt" width="20" height="20" />{" "}
                      {bookDetails?.final_price ? bookDetails?.final_price : 0}
                    </span>
                    {!bookDetails?.have_bought && (
                      <div className="flex flex-col sm:flex-row items-center gap-3 mt-2">
                        <button
                          onClick={() => handleBuyNow(bookDetails)}
                          className="btn px-6 py-1 rounded w-full sm:w-auto bg-indigo-700 text-white"
                        >
                          {t("buy.now")}
                        </button>
                        <button
                          onClick={() => handleAddToCart(bookDetails)}
                          className={`btn rounded w-full sm:w-auto bg-white text-indigo-700 border border-indigo-700 ${
                            isBookAddedToCart
                              ? "cursor-not-allowed opacity-50 bg-gray-500  px-4 py-1"
                              : " px-6 py-1"
                          }`}
                        >
                          {isBookAddedToCart
                            ? t("added.to.cart")
                            : t("add.to.cart")}
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {bookDetails?.format === ("hardcopy" || "both") && (
                <div className="flex flex-col sm:flex-row items-center gap-2 text-lg sm:text-2xl">
                  <div className="flex items-center">
                    <span className="flex items-center font-semibold text-red-500">
                      <Icon icon="mdi:currency-bdt" width="20" height="20" />{" "}
                      {bookDetails?.final_price
                        ? parseInt(bookDetails?.final_price)
                        : 0}
                    </span>
                    {parseInt(bookDetails?.discount) > 0 && (
                      <span className="flex items-center font-semibold line-through text-gray-400">
                        <Icon icon="mdi:currency-bdt" width="20" height="20" />{" "}
                        {bookDetails?.price ? parseInt(bookDetails?.price) : 0}
                      </span>
                    )}
                  </div>
                  {parseInt(bookDetails?.discount) > 0 && (
                    <span className="text-base text-indigo-600">
                      (
                      {bookDetails?.discount
                        ? parseInt(bookDetails?.discount)
                        : 0}
                      % {t("off")})
                    </span>
                  )}
                </div>
              )}

              <div className="flex flex-col sm:flex-row gap-3 max-w-full pt-2">
                <button
                  onClick={() => setShowReadModal(true)}
                  className="btn px-6 py-2 rounded-lg w-full bg-white text-indigo-700 border border-indigo-700 flex items-center gap-2 justify-center"
                >
                  <Icon icon="tabler:book" width="24" height="24" />{" "}
                  {t("read.a.little")}
                </button>
                {bookDetails?.have_bought ? (
                  <Link
                    target="_blank"
                    href={`${process.env.NEXT_PUBLIC_BASE_CONFIG_Redirect_URL}/flipbooks/${bookDetails?.id}?from=ecom`}
                    className="btn px-6 py-2 rounded-lg w-full bg-indigo-700 text-white flex items-center gap-2 justify-center"
                  >
                    <Icon icon="mynaui:book" width="24" height="24" /> {t('view.book')}
                  </Link>
                ) : (
                  <button
                    onClick={() => handleAddToCart(bookDetails)}
                    className="btn px-6 py-2 rounded-lg w-full bg-indigo-700 text-white flex items-center gap-2 justify-center"
                  >
                    <Icon icon="mynaui:book" width="24" height="24" />{" "}
                    {t("add.to.cart")}
                  </button>
                )}
                {/* <button className="btn px-6 py-2 rounded-lg w-full bg-indigo-700 text-white flex items-center gap-2 justify-center">
              <Icon icon="mdi-light:cart" width="24" height="24" /> কার্টে যোগ
              করুন
            </button> */}
              </div>
              <div className="flex items-center gap-5 pt-2">
                <p
                  className={
                    bookDetails?.is_wishlist
                      ? "cursor-not-allowed opacity-50"
                      : "cursor-pointer"
                  }
                  onClick={() =>
                    isAuth
                      ? !bookDetails?.is_wishlist &&
                        handleAddToWishlist(bookDetails?.id)
                      : router.push(
                          "/login?returnTo=/book-details/" + bookDetails?.slug
                        )
                  }
                >
                  <Icon
                    icon={
                      bookDetails?.is_wishlist
                        ? "ant-design:heart-filled"
                        : "solar:heart-linear"
                    }
                    width="18"
                    height="18"
                    className="inline mb-1 mr-2"
                  />
                  {bookDetails?.is_wishlist
                    ? t("added.to.wishlist")
                    : t("add.to.wishlist")}
                </p>

                <p onClick={handleShare} className="cursor-pointer">
                  <Icon
                    icon="basil:share-outline"
                    width="24"
                    height="24"
                    className="inline mb-1 mr-2"
                  />
                  {t('share.this.book')}
                </p>
              </div>
            </div>
          </div>

          {bookDetails?.author_books?.length > 0 && (
            <div className="col-span-12 lg:col-span-5 w-full xl:max-w-[34%] min-w-[34%] mt-5 lg:mt-0">
              <h3 className="text-lg md:text-xl font-semibold text-gray-600 text-center border-b border-gray-300 pb-2">
                {t("other.books.of.author")}
              </h3>

              <div className="max-h-80 overflow-y-auto mt-2 space-y-2 pr-3">
                {bookDetails?.author_books?.map((book, idx) => (
                  <div
                    key={idx}
                    onClick={() => router.push(`/book-details/${book?.slug}`)}
                    className="border border-gray-300 hover:border-gray-400 cursor-pointer rounded-lg flex items-center p-3 gap-3"
                  >
                    <div className="relative">
                      <Image
                        className="w-16 max-h-[85px] rounded"
                        src={
                          book?.cover_image_url ||
                          "/assets/all-images/book-2.png"
                        }
                        alt={book?.title}
                        height={400}
                        width={200}
                      />
                    </div>
                    <div className="space-y-1">
                      <h2 className="text-md text-gray-700">{book?.title}</h2>
                      <p className="text-xs">
                        {book?.author ? book.author : "Unknown"}
                      </p>
                      {/* <p className="text-xs">--</p> */}
                      <div className="flex items-center gap-2 text-lg">
                        <div className="flex items-center">
                          <span className="flex items-center font-semibold text-red-500">
                            <Icon
                              icon="mdi:currency-bdt"
                              width="20"
                              height="20"
                            />{" "}
                            {book?.final_price
                              ? parseInt(book?.final_price)
                              : 0}
                          </span>
                          <span className="flex items-center font-semibold line-through text-gray-400">
                            <Icon
                              icon="mdi:currency-bdt"
                              width="20"
                              height="20"
                            />{" "}
                            {book?.price ? parseInt(book?.price) : 0}
                          </span>
                        </div>
                        <span className="text-base text-indigo-600">
                          ({book?.discount ? parseInt(book?.discount) : 0}%
                          {t('discount')})
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <BookDetailTabs bookDetails={bookDetails} />

        {bookDetails?.category_books?.length > 0 && <BookCategory
          categoryTitle={t("related.books")}
          books={bookDetails?.category_books}
          loading={isLoading}
          removeYSpace={true}
        />}

        <BookReviews bookDetails={bookDetails} />
      </section>

      {showReadModal && (
        <ReadModal
          bookDetails={bookDetails}
          setShowReadModal={setShowReadModal}
          isBookAddedToCart={isBookAddedToCart}
        />
      )}
    </div>
  );
};

export default BookDetails;
