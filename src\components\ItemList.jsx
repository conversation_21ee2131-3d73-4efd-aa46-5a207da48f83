"use client";

import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useRouter } from "next/navigation";
import useDataFetch from "@/hooks/useDataFetch";
import Image from "next/image";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import Pagination from "./Pagination";

const ItemList = ({
  endPoint,
  navigateTitle,
  title,
  seeDetailTitle,
  detailLink,
  data = [],
  isLoading = false,
}) => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(1);

  const totalPages = data?.total_pages || 1;
  console.log(isLoading)

  useEffect(() => {
    const params = new URLSearchParams();
    if (searchTerm?.length > 0) {
      params.set("search", searchTerm);
    }
    if(currentPage !== 1){
      params.set("page", currentPage);
    }
    router.push(`?${params.toString()}`, undefined, { shallow: true });
  }, [searchTerm, currentPage]);

  // const {
  //   data,
  //   isLoading: isListLoading,
  //   refetch: isListFetching,
  // } = useDataFetch({
  //   queryKey: [`${endPoint.split("/")?.join("-")}`],
  //   endPoint: `${endPoint}${
  //     searchTerm?.length > 0 ? `?search=${searchTerm}` : ""
  //   }`,
  //   params: { pagination: false },
  // });

  // useEffect(() => {
  //   isListFetching();
  // }, [searchTerm, isListFetching]);

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0">
      <div className="py-5 space-y-5 mb-12">
        <h2
          onClick={() => router.back()}
          className="text-base flex items-center gap-2 hover:gap-3 transition-all duration-300 hover:text-indigo-600 cursor-pointer"
        >
          <Icon icon="mingcute:arrow-left-line" width="24" height="24" />{" "}
          {navigateTitle}
        </h2>

        <div
          style={{
            background: "linear-gradient(270deg, #4136F1 0%, #8743FF 68%)",
          }}
          className="text-white flex sm:items-center justify-between gap-2 flex-col sm:flex-row text-start p-3 px-4 rounded-lg"
        >
          <h2 className="text-2xl font-semibold">{title}</h2>

          <div className="flex items-center group border bg-white rounded-md overflow-hidden text-gray-700 lg:w-1/3 focus-within:border-blue-500 focus-within:bg-indigo-300">
            <input
              type="text"
              placeholder={`Search ${title}...`}
              className="w-full px-3 py-2 border-0 outline-0"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <button className="px-4">
              <Icon
                icon="cuida:search-outline"
                className="text-xl text-gray-400 group-focus-within:text-white"
              />
            </button>
          </div>

          {/* <div className="flex items-center  gap-4">

            <select name="" id="" className="py-1 rounded text-gray-400 px-2">
              <option value="value">Capital </option>
            </select>
          </div> */}
        </div>

        {/* Authors List */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
          {isLoading ? (
            <p>{t("loading")}...</p>
          ) : data?.data?.length > 0 ? (
            data?.data?.map((item) => (
              <div
                key={item.id}
                className="p-4 py-3 hover:scale-105 flex items-center gap-3 bg-[#EEEDFF] rounded-lg hover:shadow-lg border text-gray-700 cursor-pointer group hover:border-indigo-400 transition-all duration-300"
              >
                <Image
                  src={
                    title === "Categories"
                      ? item?.thumbnail_url
                        ? item?.thumbnail_url
                        : "/assets/all-images/book-icon.png"
                      : item?.user?.profile_picture_url
                      ? item?.user?.profile_picture_url
                      : "/assets/all-images/profileAvater.png"
                  }
                  alt=""
                  height={100}
                  width={100}
                  className="w-14 h-14 object-cover border-2 border-gray-300 group-hover:border-indigo-400 transition-all duration-300 rounded-full"
                />
                <div>
                  <h3 className="text-lg font-semibold mt-2">{item.name}</h3>
                  <Link
                    href={detailLink + item?.slug}
                    className="flex items-center gap-2 group-hover:gap-3 text-sm transition-all duration-300 group-hover:text-indigo-500 text-gray-600"
                  >
                    {t("see.all.books")}
                    <Icon icon="ep:right" width="14" height="14" />
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <p className="text-center col-span-full text-gray-500">
              {t("not.available")}
            </p>
          )}
        </div>

        {totalPages > 1 && (
          <div className="mt-4 flex justify-center">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ItemList;
