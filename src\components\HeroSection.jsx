"use client";

import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import { Icon } from "@iconify/react";
import Image from "next/image";

const slides = [
  { id: 1, image: "/assets/all-images/bannerr-1.png", text: "Slide 1" },
  { id: 2, image: "/assets/all-images/bannerr-2.png", text: "Slide 2" },
  { id: 3, image: "/assets/all-images/bannerr-3.png", text: "Slide 3" },
  { id: 4, image: "/assets/all-images/slider-4.png", text: "Slide 4" },
];

const HeroSection = () => {
  return (
    <div className=" max-w-7xl mx-auto px-5 xl:px-0 my-5">
      <div className="w-full relative ">
        <Swiper
          modules={[Navigation, Autoplay]}
          spaceBetween={50}
          slidesPerView={1}
          navigation={{
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
          }}
          autoplay={{ delay: 4000, disableOnInteraction: false }}
          className="w-full "
        >
          {slides.map((slide) => (
            <SwiperSlide className=" bg-gray-400 z-0 rounded-lg" key={slide?.id}>
              <Image
                className=" rounded-lg w-full"
                src={slide?.image}
                priority
                height={500}
                width={1500}
                alt={slide?.text}
              />
            </SwiperSlide>
          ))}
        </Swiper>

        {/* Navigation Buttons */}
        <button className="swiper-button-prev absolute left-4 p-3 rounded-lg bg-white">
        </button>
        <button className="swiper-button-next absolute right-4 p-3 bg-white">
        </button>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 items-center max-sm:gap-2 gap-5 mt-5">
        {/* <div className="relative h-14 sm:h-16 lg:min-h-20 w-full"> */}
          <Image
            className="rounded-lg w-full"
            src="/assets/all-images/Offer-1.png"
            width={800}
            height={200}
            // fill
            // sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
            alt="Offer Image"
          />
        {/* </div>
        <div className="relative h-14 sm:h-16 lg:min-h-20 w-full"> */}
          <Image
            className="rounded-lg w-full"
            src="/assets/all-images/Offer-2.png"
            width={800}
            height={200}
            // fill
            // sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
            alt="Offer Image"
          />
        {/* </div>
        <div className="relative h-14 sm:h-16 lg:min-h-20 w-full"> */}
          <Image
            className="rounded-lg w-full"
            src="/assets/all-images/Offer-3.png"
            width={800}
            height={200}
            // fill
            // sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
            alt="Offer Image"
          />
        {/* </div>
        <div className="relative h-14 sm:h-16 lg:min-h-20 w-full"> */}
          <Image
            className="rounded-lg w-full"
            src="/assets/all-images/Offer-4.png"
            width={800}
            height={200}
            // fill
            // sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
            alt="Offer Image"
          />
        {/* </div> */}
      </div>
    </div>
  );
};

export default HeroSection;
