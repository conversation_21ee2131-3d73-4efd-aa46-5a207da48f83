"use client";

import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslation } from "react-i18next";
import { loginSuccess } from "@/store/features/authSlice";
import api from "@/lib/api";
import Link from "next/link";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import FormInput from "@/components/form/FormInput";
import { Icon } from "@iconify/react";

export default function LoginPage() {
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const returnTo = searchParams.get("returnTo") || "/";
  const { t } = useTranslation();
  const [showPassword, setShowPassword] = useState(false);

  const LoginSchema = Yup.object().shape({
    email: Yup.string()
      .email(t("auth.sign.up.invalid.email"))
      .required(t("auth.required")),
    password: Yup.string()
      .min(8, t("auth.sign.up.atleast.8"))
      .required(t("auth.required")),
  });

  const handleLogin = async (values, { setSubmitting, setErrors }) => {
    try {
      const response = await api.post("/login", values);

      dispatch(
        loginSuccess({
          token: response.token,
          user: response.user
        })
      );

      // Wait for a small delay to ensure state is updated
      await new Promise((resolve) => setTimeout(resolve, 100));
      // Finally navigate to the return URL
      router.push(returnTo);
      window.location.href = returnTo;
    } catch (error) {
      setErrors({
        general:
          error?.response?.data?.message || "Something went wrong while login!",
      });
      setSubmitting(false);
    }
  };

  return (
    <div>
      <h2 className="text-2xl font-bold text-center mb-8">
        {t("auth.login.title")}
      </h2>

      <Formik
        initialValues={{ email: "", password: "" }}
        validationSchema={LoginSchema}
        onSubmit={handleLogin}
      >
        {({ isSubmitting, errors }) => (
          <Form className="space-y-4">
            {errors.general && (
              <div className="text-red-500 text-sm text-center">
                {errors.general}
              </div>
            )}

            <FormInput
              name="email"
              label={t("profile.email")}
              type="email"
              placeholder={t("profile.write.email")}
            />

            <div className="relative">
              <FormInput
                name="password"
                label={t("profile.password")}
                type={showPassword ? "text" : "password"}
                placeholder={t("profile.write.password")}
              />

              <Icon
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-9 text-gray-600 cursor-pointer"
                icon={
                  showPassword ? "iconamoon:eye-off-light" : "clarity:eye-line"
                }
                width="18"
                height="18"
              />
            </div>

            <span>
              <Link
                href={"/verify-email"}
                className="text-xs flex justify-end hover:text-indigo-500 mt-2"
              >
                {t("auth.forgot.password")}
              </Link>
            </span>

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full py-2 px-4 rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 disabled:bg-indigo-400"
            >
              {isSubmitting ? `${t("loading")}...` : t("login")}
            </button>
          </Form>
        )}
      </Formik>

      <div className="mt-6 text-center text-sm">
        <p className="text-gray-600">
          {t("auth.no.account")}{" "}
          <Link
            href={`/register?${searchParams}`}
            className="font-medium text-indigo-600 hover:text-indigo-500"
          >
            {t("register")}
          </Link>
        </p>
      </div>
    </div>
  );
}
