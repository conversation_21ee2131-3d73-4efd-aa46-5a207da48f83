import { Icon } from "@iconify/react";
import { useState } from "react";
import useDataFetch from "@/hooks/useDataFetch";
import Image from "next/image";
import React from "react";
import Link from "next/link";
import Pagination from "@/components/Pagination";
import Loading from "@/app/loading";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import Skeleton from "react-loading-skeleton";

const Reviews = () => {
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(1);
  const { data, isLoading, error } = useDataFetch({
    queryKey: ["my-review-list", currentPage],
    endPoint: `/reviews?page=${currentPage}`,
  });

  // if (isLoading) {
  //   return <Loading />;
  // }

  const reviews = data?.data || [];

  const generateStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating - fullStars >= 0.5;
    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <Icon
            key={`star-full-${i}`}
            icon="octicon:star-fill-16"
            width="24"
            height="24"
            className="text-[#F59E0B]"
          />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <Icon
            key="star-half"
            icon="fluent:star-half-24-regular"
            width="26"
            height="26"
            className="text-[#F59E0B]"
          />
        );
      } else {
        stars.push(
          <Icon
            key={`star-empty-${i}`}
            icon="octicon:star-16"
            width="24"
            height="24"
            className="text-[#F59E0B] text-opacity-40"
          />
        );
      }
    }
    return stars;
  };

  const ReviewItem = ({ review }) => {
    const [seeMore, setSeeMore] = useState(false);
    const comment = review.comment || "";
    const router = useRouter();
    return (
      <div className="w-full flex items-center flex-col md:flex-row gap-3 border-gray-300">
        <Image
          src={
            review?.ebook?.cover_image_url || "/assets/all-images/book-2.png"
          }
          alt={review?.ebook?.title || "reviewed book image"}
          width={50}
          height={50}
          className="min-w-16 lg:min-w-20 max-h-28 rounded-lg"
        />
        <div className="text-sm space-y-1 text-gray-500 w-full">
          <div className="flex max-sm:items-start items-center max-sm:flex-col justify-between w-full">
            <h2 className="text-lg text-gray-800">{review?.ebook?.title}</h2>
            <span className="flex items-center gap-1">
              {generateStars(review?.rating)}
            </span>
          </div>
          <p>
            {comment.length > 200 ? (
              <>
                {seeMore ? comment : comment.slice(0, 300)}{" "}
                <button
                  onClick={() => setSeeMore(!seeMore)}
                  className="text-indigo-600"
                >
                  {seeMore ? "See Less" : "See More"}
                </button>
              </>
            ) : (
              comment
            )}
          </p>
          <div className="flex gap-3 md:justify-end">
            <button
              onClick={() =>
                router.push("/book-details/" + review?.ebook?.slug)
              }
              className="px-4 py-1.5 rounded border border-indigo-600 text-indigo-600"
            >
              {t("view.book")}
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div>
      <div className="flex items-center justify-between w-full pb-5 border-b border-gray-300">
        <h2 className="text-xl font-semibold text-indigo-600">
          {t("profile.my.reviews")}
        </h2>
      </div>
      {isLoading ? (
        <div className=" flex items-start justify-center">
          <div className="w-full max-w-7xl mx-auto p-5 2xl:px-0 flex gap-6">
            {/* Left Side (Book Image) */}
            <div className="w-32 h-36">
              <Skeleton height="100%" width="100%" borderRadius={8} />
            </div>

            {/* Right Side (Book Details) */}
            <div className="flex-1">
              {/* Title */}
              <Skeleton height={40} width="60%" className="mb-2" />

              {/* Author, Categories & Published Date */}
              <Skeleton height={25} width="50%" className="mb-1" />
              <Skeleton height={25} width="40%" className="mb-1" />
              <Skeleton height={25} width="30%" className="mb-4" />
            </div>
          </div>
        </div>
      ) : (
        <div className="py-2 space-y-4">
          {reviews?.length > 0 ? (
            reviews.map((review) => (
              <ReviewItem key={review.id} review={review} />
            ))
          ) : (
            <p className="text-center">{t("no.review.found")}</p>
          )}
        </div>
      )}
      {data?.total_pages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={data.data.total_pages}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
};

export default Reviews;
