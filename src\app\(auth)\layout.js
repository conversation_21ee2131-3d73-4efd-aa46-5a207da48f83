"use client";

import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useTranslation } from "react-i18next";
import { loginSuccess } from "@/store/features/authSlice";
import api from "@/lib/api";
import Image from "next/image";
import { GoogleLogin, GoogleOAuthProvider } from "@react-oauth/google";
import { Icon } from "@iconify/react";
import { toast } from "sonner";
import { GOOGLE_CLIENT_ID } from "@/config";

export default function AuthLayout({ children }) {
  const dispatch = useDispatch();
  const router = useRouter();
  const pathName = usePathname();
  const searchParams = useSearchParams();
  const returnTo = searchParams.get("returnTo") || "/";
  const { t } = useTranslation();

  const isResetPassPage = pathName.startsWith("/password-reset/");

  const handleGoogleLogin = async (response) => {
    try {
      // console.log("Google login response:", response);
      const apiResponse = await api.post("/auth/google/callback", {
        token: response?.credential,
      });
      if (apiResponse?.success) {
        dispatch(
          loginSuccess({
            user: apiResponse.user,
            token: apiResponse.token,
          })
        );
        document.cookie = `token=${apiResponse.token}; path=/; max-age=${
          7 * 24 * 60 * 60
        }`;
        toast.success(apiResponse?.message);
        router.push(returnTo);
      }
    } catch (error) {
      console.error("Google login failed:", error);
    }
  };

  return (
    <div className="min-h-screen relative">
      <div className="flex flex-col lg:flex-row h-screen">
        <div className="relative w-full h-full max-sm:hidden flex-1 overflow-hidden rounded-r-xl">
          <Image
            src="/assets/all-images/authImg.png"
            alt="Auth background"
            fill
            className="object-cover"
            priority
          />
        </div>

        <div className="flex-1 relative flex items-center justify-center p-5">
          {/* {!isResetPassPage &&  */}
          <span onClick={() => router.back()} className="absolute top-5 left-5 cursor-pointer bg-white rounded px-2 py-1 hover:text-indigo-600"><Icon icon="eva:arrow-back-fill" width="18" height="18" className="inline mb-1" /> {t('go.back')}</span>
          {/* } */}

          <div className="w-full max-w-[375px] p-5 m-2">
            {children}

            {!isResetPassPage && <div>
              <div className="flex justify-between items-center mt-4">
                <hr className="flex-grow border-t border-gray-300" />
                <span className="px-4 text-sm">{t('or')}</span>
                <hr className="flex-grow border-t border-gray-300" />
              </div>

              <div className="mt-4 flex justify-center">
                <GoogleOAuthProvider
                  clientId={GOOGLE_CLIENT_ID}
                >
                  <GoogleLogin
                    onSuccess={handleGoogleLogin}
                    onError={() => console.error("Google login failed")}
                    className="w-full"
                  />
                </GoogleOAuthProvider>
              </div>
            </div>}
          </div>
        </div>
      </div>
    </div>
  );
}
