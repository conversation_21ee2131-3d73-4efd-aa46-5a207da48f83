import React from "react";
import { Icon } from "@iconify/react";
import footerBG from "../../../public/assets/all-images/footerBG1.png";
import Image from "next/image";
import Link from "next/link";
import { useTranslation } from "react-i18next";

const Footer = () => {
  const {t} = useTranslation();
  return (
    <div
      className="min-h-96 bg-cover no-repeat bg-center text-black py-10 lg:py-16 w-full text-[#1D769C]"
      style={{ backgroundImage: `url(${footerBG.src})` }}
    >
      <div className="max-w-7xl mx-auto grid grid-cols-12 gap-5 p-5 xl:px-0">
        {/* Company Info */}
        <div className="space-y-3 col-span-12 lg:col-span-5">
          <div className="relative w-40 h-12">
            <Image
              src="/assets/all-images/ebookLogo.png"
              alt=""
              width={400}
              height={250}
              className="object-contain"
              priority
            />
          </div>
          <p className="text-gray-400">
            {t('footer.details')}
          </p>

          <div className="flex items-center flex-wrap gap-3 text-gray-600">
            <div>
              <h4 className="font-semibold ">{t('our.phone')}</h4>
              <p>+88 01836149699</p>
            </div>

            <div className="flex items-center gap-2 text-[#0D2E44]">
              {/* <Icon icon="mdi:instagram" width="24" height="24" /></Link> */}
              <Link href={'https://www.facebook.com/YourePub'} target="_blank"><Icon icon="akar-icons:facebook-fill" width="22" height="22" /></Link>
              <Link href={'https://www.linkedin.com/company/yourepub'} target="_blank"><Icon icon="mdi:linkedin" width="28" height="28" /></Link>
              <Link href={'https://www.youtube.com/@YourePub'} target="_blank"><Icon icon="si:youtube-fill" width="28" height="28" /></Link>
            </div>
          </div>
        </div>

        {/* Quick Links */}
        <div className="col-span-12 lg:col-span-2">
          <h2 className="text-xl font-semibold mb-3">{t('footer.links')}</h2>
          <div className="flex flex-col gap-3 text-gray-500">
            <span>
              <Link href="/">{t('home')}</Link>
            </span>
            <span>
              <Link href="/about-us">{t('footer.links.about.us')}</Link>
            </span>
            <span>
              <Link href="/our-clients">{t('footer.links.our.clients')}</Link>
            </span>
            <span>
              <Link href="/software-development">{t('footer.links.software')}</Link>
            </span>
          </div>
        </div>

        {/* Help & Support */}
        <div className="col-span-12 lg:col-span-2">
          <h2 className="text-xl font-semibold mb-3">{t('footer.help')}</h2>
          <div className="flex flex-col gap-3 text-gray-500">
            <span>
              <Link href="/support">{t('footer.help.support')}</Link>
            </span>
            <span>
              <Link href="/delivery-details">{t('footer.help.delivery')}</Link>
            </span>
            <span>
              <Link href="/terms-and-conditions">{t('footer.help.terms')}</Link>
            </span>
            <span>
              <Link href="/policy-procedure">{t('footer.links.policy')}</Link>
            </span>
            {/* <span>
              <Link href="/">{t('footer.help.policy')}</Link>
            </span> */}
          </div>
        </div>

        {/* Location & Payment */}
        <div className="col-span-12 lg:col-span-3">
          <h2 className="text-xl font-semibold mb-3">{t('footer.location')}</h2>
          <p className="text-gray-500">
            {t('footer.location.address')}
          </p>
          <h2 className="text-xl font-semibold mt-4 mb-3">{t('footer.accept')}</h2>
          <div className="relative w-40 sm:w-40 md:w-48 lg:w-56 h-16">
            <Image
              className="object-contain"
              src="/assets/all-images/acceptedPG.png"
              height={100}
              width={300}
              alt=""
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Footer;
