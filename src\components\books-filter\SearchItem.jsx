import { Icon } from "@iconify/react";
import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

const SearchItem = ({ items = [], title, searchPlaceHolder, setSelectedItems, selectedItems, loading }) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredItems, setFilteredItems] = useState([]);

  // Ensure filteredItems updates when items change
  useEffect(() => {
    setFilteredItems(items);
  }, [items?.length]);

  const handleFilterItem = (value) => {
    setSearchTerm(value);
    const filtered = items.filter((item) =>
      item?.name?.toLowerCase().includes(value.toLowerCase())
    );
    setFilteredItems(filtered);
  };

  const handleSelectItem = (id) => {
    setSelectedItems((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  return (
    <div className="border rounded p-4 border-gray-300">
      <span className="border-b-2 pb-1 text-lg font-semibold text-gray-600 border-indigo-500 mb-4">
        {title}
      </span>
      <div className="hidden md:flex items-center group border rounded-md overflow-hidden border focus-within:border-indigo-500 bg-indigo-500 text-gray-700 w-full mt-4">
        <input
          type="text"
          placeholder={searchPlaceHolder}
          value={searchTerm}
          onChange={(e) => handleFilterItem(e.target.value)}
          className="w-full px-3 py-1.5 outline-0 bg-gray-100"
        />
        <button className="px-4 text-white">
          <Icon icon="mdi:magnify" className="text-xl group-focus:text-white" />
        </button>
      </div>
      <div className="space-y-3 mt-3 max-h-48 overflow-y-auto">
        {loading ? <p>Loading...</p> : filteredItems?.length > 0 ? (
          filteredItems.map((item) => (
            <label key={item?.id} className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                className="form-checkbox"
                checked={selectedItems.includes(item?.id)}
                onChange={() => handleSelectItem(item?.id)}
              />
              <span className="text-sm">{item?.name}</span>
            </label>
          ))
        ) : (
          <p className="text-xs text-gray-500">{t("not.found")}</p>
        )}
      </div>
    </div>
  );
};

export default SearchItem;
