import React from "react";
import { Icon } from "@iconify/react";
import { useAppDispatch } from "@/hooks/reduxHooks";
import { setShowModal } from "@/store/features/commonSice";

const ComingModal = () => {
  const dispatch = useAppDispatch();
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
      <div className="relative bg-white rounded-lg shadow-xl p-8 max-w-md w-full m-4 animate-fadeIn">
        <button
          onClick={() => dispatch(setShowModal(false))}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 transition-colors"
        >
          <Icon icon="mdi:close" width="24" height="24" />
        </button>

        {/* Coming Soon Text */}
        <div className="text-center py-8 space-y-2">
          <Icon icon="fluent-color:code-16" width="60" height="60" className="mx-auto " />
          <h2 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600">
            Coming Soon
          </h2>
          <p className="text-gray-600">
            We're working hard to bring you something amazing!
          </p>
        </div>
      </div>
    </div>
  );
};

export default ComingModal;
