"use client";
import FormInput from "@/components/form/FormInput";
import api from "@/lib/api";
import { Icon } from "@iconify/react";
import { Form, Formik } from "formik";
import Link from "next/link";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import * as Yup from "yup";

const LoginSchema = Yup.object().shape({
  password: Yup.string()
    .min(6, "Password too short")
    .required("Password is required"),
  password_confirmation: Yup.string()
    .oneOf([Yup.ref("password"), null], "Passwords must match")
    .required("Confirmation is required"),
});

const PasswordReset = () => {
  const { t } = useTranslation();
  const { token } = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams.get("email");

  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleResetPassword = async (values) => {
    try {
      setIsLoading(true);
      const response = await api.post(`/reset-password`, values);
      if(response?.status) {
        router.push("/login");
      };
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <div>
      <h2 className="text-2xl font-bold text-center mb-8">{t('auth.reset.password')}</h2>

      <Formik
        initialValues={{
          token: token,
          email: email,
          password: "",
          password_confirmation: "",
        }}
        validationSchema={LoginSchema}
        onSubmit={handleResetPassword}
      >
        {({ errors }) => (
          <Form className="space-y-4">
            {errors.general && (
              <div className="text-red-500 text-sm text-center">
                {errors.general}
              </div>
            )}

            <div className="relative">
              <FormInput
                name="password"
                label={t("profile.password")}
                type={showPassword ? "text" : "password"}
                placeholder={t("profile.write.password")}
              />

              <Icon
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-9 text-gray-600 cursor-pointer"
                icon={
                  showPassword ? "iconamoon:eye-off-light" : "clarity:eye-line"
                }
                width="18"
                height="18"
              />
            </div>

            <div className="relative">
              <FormInput
                name="password_confirmation"
                label={t("confirm.password")}
                type={showPassword ? "text" : "password"}
                placeholder={t("profile.write.password")}
              />

              <Icon
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-9 text-gray-600 cursor-pointer"
                icon={
                  showPassword ? "iconamoon:eye-off-light" : "clarity:eye-line"
                }
                width="18"
                height="18"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-2 px-4 rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 disabled:bg-indigo-400"
            >
              {isLoading ? `${t("loading")}...` : "Submit"}
            </button>
          </Form>
        )}
      </Formik>

      {/* <div className="mt-6 text-center text-sm">
        <p className="text-gray-600">
          {t("auth.no.account")}{" "}
          <Link
            href={`/register?${searchParams}`}
            className="font-medium text-indigo-600 hover:text-indigo-500"
          >
            {t("register")}
          </Link>
        </p>
      </div> */}
    </div>
  );
};

export default PasswordReset;
