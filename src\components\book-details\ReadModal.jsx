import { addToCart } from '@/store/features/cartSlice';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';

const ReadModal = ({bookDetails, setShowReadModal, isBookAddedToCart }) => {
    const {t} = useTranslation();
    const dispatch = useDispatch();
    const router = useRouter();


    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="relative bg-white rounded-lg shadow-lg  max-w-[650px] lg:min-w-[800px] lg:min-h-[80vh] max-h-[90vh] overflow-auto m-5">
            {/* Close Button */}
            <button
              onClick={() => setShowReadModal(false)}
              className="absolute top-4 right-4 text-gray-600 hover:text-gray-800"
            >
              ✖
            </button>

            {/* Book Details */}
            <div className="border border-gray-300 hover:border-gray-400 rounded-lg flex items-center justify-center flex-col md:flex-row p-3 gap-3 w-full">
              <div className="relative">
                <Image
                  className="w-20 rounded"
                  src={
                    bookDetails?.cover_image
                      ? process.env.NEXT_PUBLIC_BASE_URL +
                        "/storage/" +
                        bookDetails?.cover_image
                      : "/assets/all-images/book-2.png"
                  }
                  alt={bookDetails?.title}
                  height={400}
                  width={200}
                />
              </div>
              <div className="space-y-1">
                <h2 className="text-lg max-sm:text-sm text-gray-700">
                  {bookDetails?.title}
                </h2>
                <p className="text-sm max-sm:text-xs">
                  {bookDetails?.authors?.length > 0
                    ? bookDetails?.authors
                        ?.map((author) => author?.name)
                        .join(", ")
                    : bookDetails?.author
                    ? bookDetails.author
                    : "Unknown"}
                </p>
                <p className="text-sm max-sm:text-xs">
                  {bookDetails?.categories?.length > 0
                    ? bookDetails?.categories
                        ?.map((category) => category?.name)
                        .join(", ")
                    : bookDetails?.category
                    ? bookDetails.category
                    : "Unknown"}
                </p>
                <div className="flex items-center gap-2 text-lg">
                  <span className="flex items-center font-semibold text-red-500">
                    <Icon icon="mdi:currency-bdt" width="20" height="20" />{" "}
                    {bookDetails?.final_price
                      ? parseInt(bookDetails?.final_price)
                      : 0}
                  </span>
                  <span className="flex items-center font-semibold line-through text-gray-400">
                    <Icon icon="mdi:currency-bdt" width="20" height="20" />{" "}
                    {bookDetails?.price ? parseInt(bookDetails?.price) : 0}
                  </span>
                  <span className="text-base text-indigo-600">
                    (
                    {bookDetails?.discount
                      ? parseInt(bookDetails?.discount)
                      : 0}
                    % {t("discount")})
                  </span>
                </div>

                <div className="flex items-center justify-center flex-col sm:flex-row gap-3">
                  <button
                    onClick={() =>
                      dispatch(addToCart({ ...bookDetails, quantity: 1 }))
                    }
                    disabled={isBookAddedToCart}
                    className={`flex-1 btn px-6 py-2 rounded-lg w-full bg-indigo-700 text-white flex items-center gap-2 justify-center sm:w-80 ${
                      isBookAddedToCart ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                  >
                    <Icon icon="mdi-light:cart" width="24" height="24" /> {isBookAddedToCart ? t('added.to.cart') : t('add.to.cart')}
                  </button>
                  <button
                    onClick={() =>
                      dispatch(
                        addToCart({ ...bookDetails, quantity: 1 }),
                        router.push("/cart-details")
                      )
                    }
                    className="flex-1 btn px-6 py-2 rounded-lg w-full bg-white text-indigo-700 border border-indigo-700 flex items-center gap-2 justify-center"
                  >
                    <Icon icon="tabler:book" width="24" height="24" /> {t('buy.now')}
                  </button>
                </div>
              </div>
            </div>

            {/* PDF Viewer */}
            <div className=" overflow-hidden rounded-b-lg">
              <iframe
                src="https://pdfobject.com/pdf/sample.pdf"
                title="PDF Viewer"
                className="w-full  min-h-[450px]"
                frameBorder="0"
                alt="PDF Viewer"
              ></iframe>
            </div>
          </div>
        </div>
    );
};

export default ReadModal;