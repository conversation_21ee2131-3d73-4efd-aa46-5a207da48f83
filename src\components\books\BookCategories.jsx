"use client";

import React, { useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import { Icon } from "@iconify/react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";

const BookCategories = ({ sectionTitle, categoryName, categories }) => {
  const swiperRef = useRef(null);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const router = useRouter();
  const {t} = useTranslation();

  return (
    <div className="max-w-7xl mx-auto px-5 xl:px-0 py-5 relative">
      {/* Section Title */}
      <div className="flex items-center justify-between border-b-2 py-3 border-gray-400 text-gray-600">
        <h2 className="text-2xl max-sm:text-xl font-semibold">
          {sectionTitle}
        </h2>
        {categoryName?.length > 0 && <Link
          href={categoryName === "author" ? "/authors" : categoryName === "category" ? "/categories" : "/"}
          className="flex items-center gap-2 hover:text-indigo-500 hover:gap-3 transition-all duration-300"
        >
          {t('see.more')} <Icon icon="ep:right" width="20" height="20" />
        </Link>}
      </div>

      {/* Swiper with Custom Navigation */}
      <div className="relative px-10">
        {/* For Authors */}
        {categoryName === "author" && (
          <Swiper
            onSwiper={(swiper) => {
              swiperRef.current = swiper;
              setIsBeginning(swiper.isBeginning);
              setIsEnd(swiper.isEnd);
            }}
            onSlideChange={(swiper) => {
              setIsBeginning(swiper.isBeginning);
              setIsEnd(swiper.isEnd);
            }}
            slidesPerView={1}
            spaceBetween={10}
            breakpoints={{
              380: { slidesPerView: 1 },
              640: { slidesPerView: 2 },
              768: { slidesPerView: 3 },
              1024: { slidesPerView: 4 },
            }}
            modules={[Navigation]}
            className="pb-5 mt-5"
          >
            {categories?.length > 0 &&
              categories.map((category, index) => (
                <SwiperSlide key={index}>
                  <div onClick={() => router.push(`/author/${category?.slug}`)} className="border text-white hover:text-gray-800 text-center p-3 rounded-lg cursor-pointer bg-gradient-to-br from-[#4B5CCF] to-[#6E4FA8] hover:text-gray-300 transition-all duration-300">
                    <div className="flex items-center gap-2 group">
                      <div className="relative">
                        <Image
                          className="rounded-full w-12 h-12"
                          src={category?.photo_url ? category?.photo_url : "/assets/all-images/profileAvater.png"}
                          alt="Writer Profile"
                          width={150} 
                          height={150}
                        />
                      </div>

                      <div className="text-start">
                        {window.innerWidth < 640
                          ? category?.name?.slice(0, 13) + "..."
                          : window.innerWidth < 1024
                          ? category?.name?.slice(0, 25) + "..."
                          : category?.name?.length > 30
                          ? category?.name?.slice(0, 30) + "..."
                          : category?.name}
                        <Link
                          href={`/author/${category?.slug}`}
                          className="flex items-center gap-2 group-hover:gap-3 text-xs"
                        >
                          সকল বই দেখুন
                          <Icon icon="ep:right" width="14" height="14" />
                        </Link>
                      </div>
                    </div>
                  </div>
                </SwiperSlide>
              ))}
          </Swiper>
        )}

        {/* For Other Categories */}
        {categoryName !== "author" && (
          <Swiper
            onSwiper={(swiper) => {
              swiperRef.current = swiper;
              setIsBeginning(swiper.isBeginning);
              setIsEnd(swiper.isEnd);
            }}
            onSlideChange={(swiper) => {
              setIsBeginning(swiper.isBeginning);
              setIsEnd(swiper.isEnd);
            }}
            slidesPerView={2}
            spaceBetween={10}
            breakpoints={{
              380: { slidesPerView: 2 },
              640: { slidesPerView: 3 },
              768: { slidesPerView: 4 },
              1024: { slidesPerView: 5 },
            }}
            modules={[Navigation]}
            className="pb-5 mt-5"
          >
            {categories?.length > 0 &&
              categories.map((category, index) => (
                <SwiperSlide key={index}>
                  <div onClick={() => router.push(`/category/${category?.slug}`)} className="border text-gray-700 hover:text-gray-800 text-center p-3 rounded-lg cursor-pointer hover:bg-sky-50 hover:border-blue-100 transition overflow-hidden whitespace-nowrap text-ellipsis sm:px-8 lg:px-10">
                    {/* {(() => {
                      if (window.innerWidth < 640) {
                        const maxLength = 7;
                        return category?.name.length > maxLength
                          ? category?.name.slice(0, maxLength) + "..."
                          : category?.name;
                      } else {
                        return category?.name.split(",").length > 1 ? `${category?.name.split(",")[0]}..` : category?.name?.length > 10 ? category?.name?.slice(0, 10) + "..." : category?.name;
                      }
                    })()} */}
                    {category?.name}
                  </div>
                </SwiperSlide>
              ))}
          </Swiper>
        )}

        {/* Navigation Buttons */}
        <button
          className={`absolute -left-2 ${
            categoryName === "author" ? "top-9" : "top-6"
          } -translate-y-1/2 p-2 rounded-full z-10 
          ${
            isBeginning ? "text-gray-400 cursor-not-allowed" : "text-gray-800"
          }`}
          onClick={() => swiperRef.current?.slidePrev()}
          disabled={isBeginning}
        >
          <Icon icon="mdi:chevron-left" width="24" height="24" />
        </button>
        <button
          className={`absolute -right-2 ${
            categoryName === "author" ? "top-9" : "top-6"
          } -translate-y-1/2 p-2 rounded-full z-10 
          ${isEnd ? "text-gray-400 cursor-not-allowed" : "text-gray-800"}`}
          onClick={() => swiperRef.current?.slideNext()}
          disabled={isEnd}
        >
          <Icon icon="mdi:chevron-right" width="24" height="24" />
        </button>
      </div>
    </div>
  );
};

export default BookCategories;
