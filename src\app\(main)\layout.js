'use client';

import Cart from '@/components/common/Cart';
import ComingModal from '@/components/common/ComingModal';
import Footer from '@/components/common/Footer';
import Navbar from '@/components/common/Navbar';
import { useAppSelector, useAppDispatch } from '@/hooks/reduxHooks';
import { useParams, usePathname } from 'next/navigation';

export default function MainLayout({ children }) {
  const pathName = usePathname();
  const {showModal} = useAppSelector((state) => state.common);
  const isCartDetailsPage = pathName === "/cart-details";
  
  return (
    <div className="min-h-screen flex flex-col bg-[#FCFDFF]">
      <Navbar />
      { !isCartDetailsPage && <Cart />}
      <main className="flex-grow container mx-auto">
        {children}
      </main>
      {showModal && <ComingModal />}
      <Footer />
    </div>
  );
}
