import { useField } from "formik";

const FormInput = ({ label, isTextarea, ...props }) => {
  const [field, meta] = useField(props);

  return (
    <div className="">
      <label className="block text-sm font-medium mb-1">
        {label}
      </label>
      {!isTextarea ? (
        <input
          {...field}
          {...props}
          className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500"
        />
      ) : (
        <textarea
          {...field}
          {...props}
          className="w-full px-3 py-2 border min-h-28 rounded-lg focus:ring-2 focus:ring-indigo-500"
        />
      )}
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-xs mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export default FormInput;
