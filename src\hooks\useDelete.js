// hooks/useDelete.js
import api from "../lib/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const useDelete = ({ queryKey, endpoint }) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id) => {
      const response = await api.delete(`${endpoint}/${id}`);
      return response.data;
    },
    onSuccess: () => {
      if (queryKey) {
        queryClient.invalidateQueries(queryKey);
      }
    },
    onError: (error) => {
      console.error("Error deleting item:", error);
    },
  });
};

export default useDelete;
