import Image from "next/image";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";

const BookDetailTabs = ({ bookDetails }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState(0);

  return (
    <div className="bg-white shadow border p-5 rounded space-y-3">
      <h2 className="text-2xl text-gray-700 font-semibold">{t('see.book.details')}</h2>

      {/* Tabs */}
      <div className="flex items-center my-3 border-b border-gray-300 px-2">
        {[t("summary"), t("specification"), t('profile.author')].map((item, idx) => (
          <button
            key={idx}
            className={`
              py-2 px-4 max-sm:py-1 max-sm:px-2 transition-all duration-200 text-lg max-sm:text-sm relative 
              ${
                activeTab === idx
                  ? "border-x border-t border-indigo-500 rounded-t-md bg-white"
                  : "border-x border-t border-transparent"
              }
            `}
            onClick={() => setActiveTab(idx)}
          >
            {item}
            {activeTab === idx && (
              <span className="absolute bottom-[-1px] left-0 w-full h-[2px] bg-white"></span>
            )}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 0 && <p>{bookDetails?.description}</p>}

        {activeTab === 1 && (
          <div className="overflow-x-auto pt-1.5">
            <table className="w-full border border-gray-300">
              <tbody>
                {[
                  { label: "Title", value: bookDetails?.title || "--" },
                  { label: "Editor", value: bookDetails?.authors?.map(item => item.name).join(', ') || "--" },
                  { label: "Publisher", value: bookDetails?.publisher || "--", link: true },
                  { label: "ISBN", value: "--" },
                  { label: "Edition", value: bookDetails?.book_type || "--" },
                  { label: "Number of Pages", value: "661" },
                  { label: "Country", value: "বাংলাদেশ" },
                  { label: "Language", value: "বাংলা" },
                ].map((item, index) => (
                  <tr key={index} className="border-b grid grid-cols-4 max-sm:grid-cols-3">
                    <td className="p-3 font-medium border-r bg-gray-100 col-span-1">{item.label}</td>
                    <td className="p-3">
                      {item.link ? (
                        <a href="#" className="text-blue-500 hover:underline">
                          {item.value}
                        </a>
                      ) : (
                        item.value
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {activeTab === 2 && (bookDetails?.authors?.length > 0 ? (
          <div>
            {bookDetails.authors.map((author, idx) => (
              <div
                key={idx}
                className={`
                  flex max-sm:flex-col sm:flex-row items-center 
                  ${idx < bookDetails.authors.length - 1 ? "border-b" : ""}
                  py-3 border-gray-300 gap-5
                `}
              >
                <Image
                  src={author?.photo_url ? author.photo_url : "/assets/all-images/profileAvater.png"}
                  alt={author?.name}
                  width={400}
                  height={400}
                  className="w-32 h-32 rounded-full object-cover"
                />
                <div>
                  <h2 className="text-2xl">{author?.name}</h2>
                  <p className="text-lg">{author?.bio}</p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p>{t("not.available")}</p>
        ))}
      </div>
    </div>
  );
};

export default BookDetailTabs;
