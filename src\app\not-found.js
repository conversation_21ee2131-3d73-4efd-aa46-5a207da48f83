import React from "react";
import Link from "next/link";
import { Icon } from "@iconify/react";

const NotFound = () => {
  return (
    <div className="h-screen flex flex-col items-center justify-center bg-gray-100 text-center px-4">
      <h1 className="text-7xl font-bold text-indigo-600 mb-4">404</h1>
      <h2 className="text-2xl font-semibold text-gray-800">Oops! Page Not Found</h2>
      <p className="text-gray-600 mt-2">
        The page you are looking for might have been removed or doesn't exist.
      </p>

      <Link
        href="/"
        className="flex items-center gap-2 mt-6 px-6 py-3 bg-indigo-600 text-white font-medium rounded-lg shadow-md hover:bg-indigo-700 transition"
      >
        Go Back Home <Icon icon="akar-icons:arrow-back" width="16" height="16" />
      </Link>
    </div>
  );
};

export default NotFound;
