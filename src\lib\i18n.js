// i18n.js
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import enTranslation from '@/locales/en.json';
import banTranslation from '@/locales/ban.json';

// English and Bangla translations
const resources = {
  En: {
    translation: enTranslation,
  },
  BN: {
    translation: banTranslation,
  },
};

i18n
  .use(initReactI18next) // Pass the i18n instance to react-i18next
  .init({
    resources,
    lng: 'En', // Default language
    interpolation: {
      escapeValue: false, // React already does escaping
    },
  });

export default i18n;
