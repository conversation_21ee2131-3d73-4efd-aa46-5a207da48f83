"use client";

import React, { useEffect, useState } from "react";
import { Icon } from "@iconify/react";
import { useDispatch, useSelector } from "react-redux";
import Image from "next/image";
import {
  removeFromCart,
} from "@/store/features/cartSlice";
import BookCategory from "@/components/books/BookCategory";
import { useTranslation } from "react-i18next";
import useSyncCart from "@/hooks/useSyncCart"; 
import OrderSuccessModal from "./_component/OrderSuccessModal";
import CartSummary from "./_component/CartSummary";
import { useRouter } from "next/navigation";
import Link from "next/link";
import ProtectedRoute from '@/components/auth/ProtectedRoute';

const CartDetails = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const router = useRouter();

  const { cart } = useSelector((state) => state.cart);
  const cartData = Array.isArray(cart) ? cart : [];
  const { isAuth } = useSelector((state) => state.auth);

  useSyncCart(cartData, isAuth);

  const uniquePublications = [
    ...new Set(cartData.map((item) => item.publication)),
  ];

  // Selection states for books and publications
  const [isAllSelected, setIsAllSelected] = useState(true);
  const [selectedBooks, setSelectedBooks] = useState([]);

  // Modified useEffect to only set initial selection
  useEffect(() => {
    // Only set initial selection if selectedBooks is empty
    if (selectedBooks.length === 0) {
      setSelectedBooks(
        cartData.map((item) => ({ 
          item_id: item.id, 
          quantity: item.quantity 
        }))
      );
    } else {
      // For subsequent cart changes, only update selections to remove deleted items
      setSelectedBooks(prev => 
        prev.filter(selected => 
          cartData.some(item => item.id === selected.item_id)
        )
      );
    }
  }, [cartData]); // Dependency on cartData ensures it updates when cart changes

  const [selectedPublications, setSelectedPublications] = useState(
    uniquePublications.reduce((acc, pub) => ({ ...acc, [pub]: true }), {})
  );
  const [isBkashChecked, setIsBkashChecked] = useState(true);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const onClose = () => {
    setShowSuccessModal(false);
    router.push('/');
  };

  const handleSelectAll = () => {
    if (!isAllSelected) {
      setSelectedBooks(
        cartData.map((item) => ({ item_id: item.id, quantity: item.quantity }))
      );
      setSelectedPublications(
        uniquePublications.reduce((acc, pub) => ({ ...acc, [pub]: true }), {})
      );
    } else {
      setSelectedBooks([]);
      setSelectedPublications({});
    }
    setIsAllSelected(!isAllSelected);
  };

  // Add this function to check if all books of a publication are selected
  const areAllPublicationBooksSelected = (publication) => {
    const publicationBooks = cartData
      .filter((item) => item.publication === publication)
      .map((item) => item.id);
    
    return publicationBooks.every((bookId) => 
      selectedBooks.some((selected) => selected.item_id === bookId)
    );
  };

  const handleSelectPublication = (publication) => {
    const publicationBooks = cartData
      .filter((item) => item.publication === publication)
      .map((item) => ({ item_id: item.id, quantity: item.quantity }));

    const isPublicationSelected = areAllPublicationBooksSelected(publication);

    setSelectedPublications((prev) => ({
      ...prev,
      [publication]: !isPublicationSelected
    }));

    setSelectedBooks((prev) => {
      if (isPublicationSelected) {
        // Remove all books of this publication
        return prev.filter(
          (book) =>
            !publicationBooks.some(
              (pubBook) => pubBook.item_id === book.item_id
            )
        );
      } else {
        // Add all books of this publication
        const existingBookIds = prev.map(book => book.item_id);
        const newBooks = publicationBooks.filter(
          book => !existingBookIds.includes(book.item_id)
        );
        return [...prev, ...newBooks];
      }
    });
  };

  const handleBookSelect = (id) => {
    const book = cartData.find((item) => item.id === id);
    if (!book) return;

    setSelectedBooks((prev) => {
      const isSelected = prev.some((b) => b.item_id === id);
      const newSelectedBooks = isSelected
        ? prev.filter((b) => b.item_id !== id)
        : [...prev, { item_id: id, quantity: book.quantity }];

      // Update publication selection state based on selected books
      const publication = book.publication;
      const publicationBooks = cartData
        .filter((item) => item.publication === publication)
        .map((item) => item.id);
      
      const areAllSelected = publicationBooks.every((bookId) =>
        newSelectedBooks.some((selected) => selected.item_id === bookId)
      );

      setSelectedPublications((prev) => ({
        ...prev,
        [publication]: areAllSelected
      }));

      return newSelectedBooks;
    });
  };

  const handleSelectedItemsRemove = () => {
    const itemsToRemove = selectedBooks.map(item => item.item_id);
    
    // Remove items one by one to maintain selection state
    itemsToRemove.forEach((itemId) => {
      dispatch(removeFromCart(itemId));
      // Update selectedBooks by removing the deleted item
      setSelectedBooks(prev => 
        prev.filter(selected => selected.item_id !== itemId)
      );
    });
  };

  // Price calculations based on Redux cart data
  const selectedBookIds = selectedBooks.map((book) => book.item_id);
  const basePrice = cartData
    .filter((book) => selectedBookIds.includes(book.id))
    .reduce(
      (total, book) =>
        total +
        (book?.final_price ? parseFloat(book.final_price) * book.quantity : 0),
      0
    );
  

  useEffect(() => {
    const updateSelectionStates = () => {
      // Check if all books are selected by comparing IDs
      const selectedBookIds = new Set(selectedBooks.map(book => book.item_id));
      const allBooksSelected = cartData.every(item => selectedBookIds.has(item.id));

      // Check if all publications are selected
      const allPublicationsSelected = uniquePublications.every(pub => 
        areAllPublicationBooksSelected(pub)
      );

      // Only update if the state would actually change
      if (isAllSelected !== (allBooksSelected && allPublicationsSelected)) {
        setIsAllSelected(allBooksSelected && allPublicationsSelected);
      }
    };

    updateSelectionStates();
  }, [selectedBooks, cartData, uniquePublications, areAllPublicationBooksSelected]);


  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0">
      {/* Cart Items Section */}
      <div className="grid grid-cols-1 p-5 md:grid-cols-12 gap-5">
        <div className="lg:col-span-9 col-span-8">
          {/* Select All Books */}
          <div className="flex items-center justify-center p-2 px-4 mb-5 bg-white shadow border rounded">
            <label
              htmlFor="all_cart"
              className="flex items-center gap-2 w-full cursor-pointer"
              onClick={handleSelectAll}
            >
              <input
                type="checkbox"
                className="form-checkbox mr-2"
                checked={isAllSelected}
                onChange={handleSelectAll}
              />
              <h2 className="text-xl font-semibold p-4">
                {t("select.all")} ({cartData.length})
              </h2>
            </label>

            <button
              className={`cursor-pointer ${
                !isAllSelected ? "hidden" : "block"
              }`}
              style={{
                opacity: isAllSelected ? 1 : 0.5,
                pointerEvents: isAllSelected ? "auto" : "none",
              }}
            >
              <Icon
                onClick={() => handleSelectedItemsRemove()}
                icon="mdi:trash-can-outline"
                className="text-red-500"
                width="20"
                height="20"
              />
            </button>
          </div>

          <div className="bg-white shadow border rounded overflow-hidden">
            {/* Dynamically Render Publications */}
            {uniquePublications?.length < 1 ? (
              <p className="p-3">{t("cart.empty")}</p>
            ) : (
              uniquePublications?.map((publication, idx) => (
                <div key={idx}>
                  <div className="flex items-center justify-between p-2 px-4 bg-white shadow-lg ">
                    <label
                      htmlFor={`all_cart_by_${publication}`}
                      className="flex items-center gap-2 w-full mb-0.5 cursor-pointer"
                      onClick={() => handleSelectPublication(publication)}
                    >
                      <input
                        type="checkbox"
                        className="form-checkbox mr-4"
                        checked={areAllPublicationBooksSelected(publication)}
                        onChange={() => handleSelectPublication(publication)}
                      />
                      <h2 className="text-xl font-semibold p-4">
                        {publication || "Publication"}
                      </h2>
                    </label>
                    {selectedPublications[publication] && (
                      <button>
                        <Icon
                          onClick={() => handleSelectedItemsRemove()}
                          icon="mdi:trash-can-outline"
                          className="text-red-500 cursor-pointer"
                          width="18"
                          height="18"
                        />
                      </button>
                    )}
                  </div>

                  <div className="max-sm:max-h-[500px] max-sm:overflow-y-scroll">
                    {cartData
                      .filter((item) => item.publication === publication)
                      .map((item, idx) => (
                        <label
                          key={idx}
                          className={`flex items-center justify-between border-b  max-sm:flex-col max-sm:items-start max-sm:gap-5 px-4 ${
                            cartData.indexOf(item) % 2 === 0
                              ? "bg-[#F4F4FF]"
                              : "bg-[#FEFEFF]"
                          } p-3`}
                        >
                          <div className="flex items-center flex-1">
                            <input
                              type="checkbox"
                              className="form-checkbox mr-4 max-sm:mr-2"
                              checked={selectedBooks.some(
                                (book) => book.item_id === item.id
                              )}
                              onChange={() => handleBookSelect(item.id)}
                            />
                            <Image
                              src={
                                item?.cover_image
                                  ? process.env.NEXT_PUBLIC_BASE_URL +
                                    "/storage/" +
                                    item.cover_image
                                  : item?.image ||
                                    "/assets/all-images/book-2.png"
                              }
                              height={150}
                              width={200}
                              alt={item?.title}
                              className="w-16 max-h-[100px] max-sm:w-12 object-cover rounded-md mr-4 max-sm:mr-2"
                            />
                            <div className="flex-grow">
                              <Link href={`/book-details/${item?.slug}`} className="font-semibold min-w-32 hover:underline">{item.title}</Link>
                              <p className="text-sm text-gray-500">
                                {item.author}
                              </p>
                              <button
                                onClick={() =>
                                  dispatch(removeFromCart(item.id))
                                }
                                className="text-red-500 border p-2 rounded-lg border-gray-300 hover:border-gray-400"
                              >
                                <Icon
                                  icon="mdi:trash-can-outline"
                                  width="20"
                                  height="20"
                                />
                              </button>
                            </div>
                          </div>

                          <div className="flex items-center xl:gap-10 gap-3 w-full max-sm:items-end max-sm:flex-col-reverse max-sm:flex-col flex-1">
                            {/* Quantity Controls */}
                            <div className="flex-1 flex items-center justify-center">
                              {/* <div className="flex items-center justify-between border border-gray-300 rounded w-28 max-sm:w-20 bg-white overflow-hidden">
                                <button
                                  onClick={() =>
                                    dispatch(decreaseQuantity(item.id))
                                  }
                                  className="px-2 py-1 bg-gray-100 text-gray-700"
                                >
                                  -
                                </button>
                                <span>{item.quantity || 1}</span>
                                <button
                                  onClick={() =>
                                    dispatch(increaseQuantity(item.id))
                                  }
                                  className="px-2 py-1 bg-gray-100 text-gray-700"
                                >
                                  +
                                </button>
                              </div> */}
                            </div>

                            <div className="text-right flex-1">
                              <p className="ml-4 font-semibold text-red-500">
                                ৳ {parseFloat(item.price) || 0}
                              </p>
                              <p className="ml-4 font-semibold text-gray-600">
                                ৳ {parseFloat(item.final_price) || 0}
                              </p>
                            </div>
                          </div>
                        </label>
                      ))}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Cart Summary Section */}
        <div className="col-span-4 lg:col-span-3 max-sm:col-span-8">
          <CartSummary basePrice={basePrice} isBkashChecked={isBkashChecked} setIsBkashChecked={setIsBkashChecked} selectedBooks={selectedBooks} setShowSuccessModal={setShowSuccessModal} handleSelectedItemsRemove={handleSelectedItemsRemove} />
        </div>
      </div>

      {/* <BookCategory books={cartData} categoryTitle={t("related.books")} /> */}
      {bookDetails?.category_books?.length > 0 && <BookCategory
          categoryTitle={t("related.books")}
          books={bookDetails?.category_books}
          loading={isLoading}
          removeYSpace={true}
        />}
      {/* // to-do: use segregated value when more payment methods are added */}
      <OrderSuccessModal isOpen={showSuccessModal} isOnlinePayment={isBkashChecked} onClose={onClose} />
    </div>
  );
};

// Wrap the component with ProtectedRoute
export default function ProtectedCartDetails() {
  return (
    <ProtectedRoute>
      <CartDetails />
    </ProtectedRoute>
  );
}
