'use client';

import React from "react";
import Books from "@/components/books/Books";
import useDataFetch from "@/hooks/useDataFetch";
import Loading from "@/app/loading";
import { useParams, useSearchParams } from "next/navigation";

const Category = () => {
  const { slug } = useParams();
  const searchParams = useSearchParams();
  const authors = searchParams.get('authors');
  const category = searchParams.get('category');
  const rating = searchParams.get('rating');

  const endpoint = slug === "recent" ? "/ebooks/recent" : `/category-books/${slug}`;
  
  const { data, isLoading } = useDataFetch({
    queryKey: ["category-books", slug, { authors, category, rating }],
    endPoint: endpoint,
    params: { authors, category, rating }
  });

  if (isLoading) return <Loading />;

  return (
    <div>
      <Books
        endPoint={endpoint}
        showAuthorFilter={true}
        showCategoryFilter={false}
        data={data}
      />
    </div>
  );
};

export default Category;
